
import json
import asyncio
import docker
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from rest_framework_simplejwt.tokens import UntypedToken, AccessToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from django.contrib.auth import get_user_model
from .models import UserContainer

User = get_user_model()

class TerminalConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        # Accept connection first, then handle authentication
        await self.accept()

        # Initialize variables
        self.user = None
        self.container = None
        self.authenticated = False
        self.current_directory = '/home/<USER>'
        self.command_buffer = ''  # Buffer for building commands

        # Send welcome message
        await self.send(text_data=json.dumps({
            'output': 'CodeForge Terminal v1.0\r\nSend your JWT token to authenticate.\r\n'
        }))

    @database_sync_to_async
    def get_user_by_token(self, token):
        try:
            # Validate and decode JWT token
            access_token = AccessToken(token)
            user_id = access_token['user_id']
            return User.objects.get(id=user_id)
        except (Invalid<PERSON>oken, TokenError, User.DoesNotExist):
            return None

    @database_sync_to_async
    def get_user_container(self):
        if self.user:
            return UserContainer.objects.filter(user=self.user).first()
        return None

    @database_sync_to_async
    def save_container_record(self, container_record):
        container_record.save()

    async def authenticate_user(self, token):
        """Authenticate user with JWT token"""
        self.user = await self.get_user_by_token(token)
        if self.user:
            self.authenticated = True
            container_record = await self.get_user_container()
            if container_record:
                await self.setup_container(container_record)
                await self.send(text_data=json.dumps({
                    'output': f'\r\n✅ Authenticated as {self.user.username}\r\n'
                }))
                await self.send_welcome_message()
                return True

        await self.send(text_data=json.dumps({
            'output': '\r\n❌ Authentication failed. Invalid token.\r\n'
        }))
        return False

    async def send_welcome_message(self):
        """Send welcome message with file listing"""
        welcome_msg = f"""
                        Welcome {self.user.username:<20}


Your isolated development environment is ready!
Type 'help' for available commands, 'ls' to see files, 'code <file>' to edit.

"""
        await self.send(text_data=json.dumps({'output': welcome_msg}))

        # Show current directory and files
        await self.execute_command('pwd && echo "Files in your workspace:" && ls -la')

    async def setup_container(self, container_record):
        """Setup Docker container in a thread-safe way"""
        loop = asyncio.get_event_loop()

        def _setup_docker():
            client = docker.from_env()
            try:
                container = client.containers.get(container_record.container_id)
                if container.status != 'running':
                    container.start()
                return container
            except docker.errors.NotFound:
                # Recreate container if it was deleted
                volume_name = container_record.volume_name
                container = client.containers.run(
                    'codeforge_ubuntu:latest',
                    command='tail -f /dev/null',
                    detach=True,
                    volumes={volume_name: {'bind': '/home/<USER>', 'mode': 'rw'}},
                    name=f'codeforge_{self.user.id}_{self.user.username}',
                    remove=False
                )
                container_record.container_id = container.id
                return container

        self.container = await loop.run_in_executor(None, _setup_docker)
        if hasattr(container_record, 'container_id'):
            await self.save_container_record(container_record)

    async def disconnect(self, close_code):
        # Keep container running for persistence
        pass

    async def receive(self, text_data):
        try:
            # Try to parse as JSON first
            try:
                data = json.loads(text_data)

                if isinstance(data, dict):
                    if 'type' in data and data['type'] == 'auth':
                        token = data.get('token', '')
                        await self.authenticate_user(token)
                        return

                    if 'input' in data:
                        await self.handle_terminal_input(data['input'])
                        return
                else:
                    if not self.authenticated:
                        await self.authenticate_user(str(data).strip())
                        return
                    await self.handle_terminal_input(str(data))

            except json.JSONDecodeError:
                # Handle raw terminal input (single characters)
                if not self.authenticated:
                    # Try to authenticate with the input as token
                    await self.authenticate_user(text_data.strip())
                    return

                # Handle raw terminal input
                await self.handle_terminal_input(text_data)

        except Exception as e:
            print(f"Terminal consumer error: {e}")  # Debug logging
            await self.send(text_data=json.dumps({
                'output': f'Error: {str(e)}\r\n$ '
            }))

    async def handle_terminal_input(self, input_data):
        """Handle terminal input - both single characters and complete commands"""
        if not self.authenticated:
            return

        # Ensure input_data is a string
        if isinstance(input_data, (int, float)):
            input_data = str(input_data)
        elif not isinstance(input_data, str):
            input_data = str(input_data)

        print(f"Terminal input received: {repr(input_data)}")  # Debug logging

        # Handle special characters
        if input_data == '\r' or input_data == '\n' or input_data == '\r\n':
            # Send newline to terminal first
            await self.send(text_data=json.dumps({'output': '\r\n'}))

            # Execute the command
            if self.command_buffer.strip():
                command = self.command_buffer.strip()
                print(f"Executing command: {command}")  # Debug logging
                self.command_buffer = ''

                # Handle special commands
                # Ensure command is a string
                command = str(command).strip()

                if command == 'help':
                    await self.send_help()
                elif command.startswith('code '):
                    filename = command[5:].strip()
                    await self.open_file_editor(filename)
                elif command == 'clear':
                    await self.send(text_data=json.dumps({'output': '\033[2J\033[H$ '}))
                elif command.startswith('run ') or command.startswith('Running '):
                    # Handle code execution
                    filename = command.split(' ', 1)[1].strip() if ' ' in command else ''
                    await self.execute_code_file(filename)

                else:
                    # Execute command in container
                    output = await self.execute_command(command)
                    await self.send(text_data=json.dumps({'output': output}))
            else:
                # Just show prompt for empty command
                await self.send(text_data=json.dumps({'output': '$ '}))

        elif input_data == '\u007F' or input_data == '\b':
            # Backspace - remove last character
            if self.command_buffer:
                self.command_buffer = self.command_buffer[:-1]
                await self.send(text_data=json.dumps({'output': '\b \b'}))

        elif input_data == '\u0003':
            # Ctrl+C - cancel current command
            self.command_buffer = ''
            await self.send(text_data=json.dumps({'output': '^C\r\n$ '}))

        elif input_data == '\u0004':
            # Ctrl+D - EOF
            await self.send(text_data=json.dumps({'output': '\r\nexit\r\n'}))
            await self.disconnect()

        elif isinstance(input_data, str) and len(input_data) == 1 and ord(input_data) >= 32:
            # Regular printable character
            self.command_buffer += input_data
            await self.send(text_data=json.dumps({'output': input_data}))

        # Handle escape sequences (arrow keys, etc.)
        elif isinstance(input_data, str) and input_data.startswith('\033['):
            # For now, ignore escape sequences like arrow keys
            # In a full implementation, you'd handle command history here
            pass

        # For complete commands (when input contains full command)
        elif isinstance(input_data, str) and len(input_data) > 1 and not input_data.startswith('\033'):
            # Send newline first
            await self.send(text_data=json.dumps({'output': '\r\n'}))

            command = input_data.strip()
            if command:
                if command == 'help':
                    await self.send_help()
                elif command.startswith('code '):
                    filename = command[5:].strip()
                    await self.open_file_editor(filename)
                elif command == 'clear':
                    await self.send(text_data=json.dumps({'output': '\033[2J\033[H$ '}))
                else:
                    output = await self.execute_command(command)
                    await self.send(text_data=json.dumps({'output': output}))
            else:
                await self.send(text_data=json.dumps({'output': '$ '}))

        else:
            # Handle any other input types safely
            try:
                if isinstance(input_data, str):
                    # Unknown string input - just ignore it
                    print(f"DEBUG: Ignoring unknown string input: {repr(input_data)}")
                else:
                    # Convert non-string input to string and handle
                    str_input = str(input_data)
                    print(f"DEBUG: Converting non-string input: {type(input_data)} -> {repr(str_input)}")
                    # If it's a single printable character, add it to buffer
                    if len(str_input) == 1 and ord(str_input) >= 32:
                        self.command_buffer += str_input
                        await self.send(text_data=json.dumps({'output': str_input}))
            except Exception as e:
                print(f"DEBUG: Error handling input {repr(input_data)}: {e}")
                # Just ignore problematic input

    async def send_help(self):
        """Send help message"""
        help_msg = """
╔══════════════════════════════════════════════════════════════╗
║                    CodeForge Terminal Help                   ║
║                   🚀 FULL ROOT ACCESS 🚀                    ║
╚══════════════════════════════════════════════════════════════╝

🎯 You have FULL ROOT ACCESS to this container!
   Run ANY command just like on your local machine:

📦 Package Management:
  apt update                    - Update package lists
  apt install nodejs npm       - Install Node.js and npm
  apt install python3-pip      - Install Python pip
  apt install docker.io        - Install Docker
  apt install mysql-server     - Install MySQL
  apt install nginx            - Install Nginx

🛠️  System Commands:
  systemctl start nginx        - Start services
  ps aux                       - Show running processes
  top                          - System monitor
  df -h                        - Disk usage
  free -h                      - Memory usage

📁 File Operations:
  ls, pwd, cd, mkdir, touch    - Basic file operations
  nano <file>, vim <file>      - Edit files
  chmod +x <file>              - Make executable
  chown user:user <file>       - Change ownership

💻 Programming:
  node <file>                  - Run JavaScript
  python3 <file>               - Run Python
  gcc <file> -o output         - Compile C
  make                         - Build projects

🌐 Network & Services:
  curl, wget                   - Download files
  ssh, scp                     - Remote access
  git clone <repo>             - Clone repositories

Type ANY Linux command and press Enter to execute!
This is YOUR container - you have complete control! 🎉

$ """
        await self.send(text_data=json.dumps({'output': help_msg}))

    async def open_file_editor(self, filename):
        """Open file in a simple editor"""
        if not filename:
            await self.send(text_data=json.dumps({
                'output': 'Usage: code <filename>\r\n$ '
            }))
            return

        # Clean the filename - remove duplicate paths
        clean_filename = filename
        if filename.startswith('/home/<USER>/'):
            clean_filename = filename
        elif filename.startswith('home/user/'):
            clean_filename = '/' + filename
        else:
            clean_filename = f'/home/<USER>/{filename}'

        print(f"DEBUG: Opening file - original: {filename}, clean: {clean_filename}")

        # Check if file exists, create if not
        check_cmd = f'test -f "{clean_filename}" && echo "exists" || touch "{clean_filename}"'
        await self.execute_command(check_cmd)

        # Show file contents
        content_output = await self.execute_command(f'cat "{clean_filename}"')

        # Get just the filename for display
        display_name = clean_filename.split('/')[-1]

        editor_msg = f"""
╔══════════════════════════════════════════════════════════════╗
║                    File: {display_name:<40}           ║
╚══════════════════════════════════════════════════════════════╝

{content_output}

Use 'nano {clean_filename}' or 'vim {clean_filename}' to edit this file.
Or use the web editor to edit this file.
$ """
        await self.send(text_data=json.dumps({'output': editor_msg}))

    async def execute_code_file(self, filename):
        """Execute code files based on their extension"""
        if not filename:
            await self.send(text_data=json.dumps({
                'output': 'Usage: run <filename>\r\nExample: run program.cpp\r\n$ '
            }))
            return

        # Clean the filename path
        if filename.startswith('/home/<USER>/'):
            clean_filename = filename
        else:
            clean_filename = f'/home/<USER>/{filename}'

        print(f"DEBUG: Executing code file: {clean_filename}")

        # Check if file exists
        check_result = self.container.exec_run(
            cmd=['test', '-f', clean_filename],
            user='user',
            workdir=self.current_directory
        )

        if check_result.exit_code != 0:
            await self.send(text_data=json.dumps({
                'output': f'Error: File {filename} not found\r\n$ '
            }))
            return

        # Determine file type and execution method
        if filename.endswith('.cpp') or filename.endswith('.cc') or filename.endswith('.cxx'):
            await self.execute_cpp_file(clean_filename, filename)
        elif filename.endswith('.py'):
            await self.execute_python_file(clean_filename, filename)
        elif filename.endswith('.js'):
            await self.execute_javascript_file(clean_filename, filename)
        elif filename.endswith('.java'):
            await self.execute_java_file(clean_filename, filename)
        else:
            await self.send(text_data=json.dumps({
                'output': f'Error: Unsupported file type for {filename}\r\nSupported: .cpp, .py, .js, .java\r\n$ '
            }))

    async def execute_cpp_file(self, filepath, filename):
        """Compile and execute C++ file"""
        try:
            # Get base name without extension
            base_name = filepath.rsplit('.', 1)[0]
            executable = f"{base_name}_exec"

            await self.send(text_data=json.dumps({
                'output': f'Compiling {filename}...\r\n'
            }))

            # Compile the C++ file
            compile_result = self.container.exec_run(
                cmd=['g++', filepath, '-o', executable],
                user='user',
                workdir=self.current_directory
            )

            if compile_result.exit_code != 0:
                error_output = compile_result.output.decode('utf-8', errors='ignore')
                await self.send(text_data=json.dumps({
                    'output': f'Compilation failed:\r\n{error_output}\r\n$ '
                }))
                return

            await self.send(text_data=json.dumps({
                'output': f'Compilation successful! Running {filename}...\r\n'
            }))

            # Execute the compiled program
            exec_result = self.container.exec_run(
                cmd=[executable],
                user='user',
                workdir=self.current_directory
            )

            output = exec_result.output.decode('utf-8', errors='ignore')
            await self.send(text_data=json.dumps({
                'output': f'{output}\r\nProgram finished with exit code {exec_result.exit_code}\r\n$ '
            }))

            # Clean up executable
            self.container.exec_run(
                cmd=['rm', '-f', executable],
                user='user',
                workdir=self.current_directory
            )

        except Exception as e:
            await self.send(text_data=json.dumps({
                'output': f'Error executing C++ file: {str(e)}\r\n$ '
            }))

    async def execute_python_file(self, filepath, filename):
        """Execute Python file"""
        try:
            await self.send(text_data=json.dumps({
                'output': f'Running Python file {filename}...\r\n'
            }))

            result = self.container.exec_run(
                cmd=['python3', filepath],
                user='user',
                workdir=self.current_directory
            )

            output = result.output.decode('utf-8', errors='ignore')
            await self.send(text_data=json.dumps({
                'output': f'{output}\r\nProgram finished with exit code {result.exit_code}\r\n$ '
            }))

        except Exception as e:
            await self.send(text_data=json.dumps({
                'output': f'Error executing Python file: {str(e)}\r\n$ '
            }))

    async def execute_javascript_file(self, filepath, filename):
        """Execute JavaScript file with Node.js"""
        try:
            await self.send(text_data=json.dumps({
                'output': f'Running JavaScript file {filename}...\r\n'
            }))

            result = self.container.exec_run(
                cmd=['node', filepath],
                user='user',
                workdir=self.current_directory
            )

            output = result.output.decode('utf-8', errors='ignore')
            await self.send(text_data=json.dumps({
                'output': f'{output}\r\nProgram finished with exit code {result.exit_code}\r\n$ '
            }))

        except Exception as e:
            await self.send(text_data=json.dumps({
                'output': f'Error executing JavaScript file: {str(e)}\r\n$ '
            }))

    async def execute_java_file(self, filepath, filename):
        """Compile and execute Java file"""
        try:
            # Get directory and class name
            directory = '/'.join(filepath.split('/')[:-1])
            class_name = filename.rsplit('.', 1)[0]

            await self.send(text_data=json.dumps({
                'output': f'Compiling Java file {filename}...\r\n'
            }))

            # Compile Java file
            compile_result = self.container.exec_run(
                cmd=['javac', filepath],
                user='user',
                workdir=self.current_directory
            )

            if compile_result.exit_code != 0:
                error_output = compile_result.output.decode('utf-8', errors='ignore')
                await self.send(text_data=json.dumps({
                    'output': f'Compilation failed:\r\n{error_output}\r\n$ '
                }))
                return

            await self.send(text_data=json.dumps({
                'output': f'Compilation successful! Running {filename}...\r\n'
            }))

            # Execute Java program
            exec_result = self.container.exec_run(
                cmd=['java', '-cp', directory, class_name],
                user='user',
                workdir=self.current_directory
            )

            output = exec_result.output.decode('utf-8', errors='ignore')
            await self.send(text_data=json.dumps({
                'output': f'{output}\r\nProgram finished with exit code {exec_result.exit_code}\r\n$ '
            }))

            # Clean up class file
            self.container.exec_run(
                cmd=['rm', '-f', f'{directory}/{class_name}.class'],
                user='user',
                workdir=self.current_directory
            )

        except Exception as e:
            await self.send(text_data=json.dumps({
                'output': f'Error executing Java file: {str(e)}\r\n$ '
            }))

    async def execute_streaming_command(self, command):
        """Execute command with live streaming output"""
        print(f"DEBUG: Starting streaming command: {command}")

        try:
            # Start the command in background
            result = self.container.exec_run(
                cmd=['bash', '-c', f'timeout 300 {command} || echo "[Command completed or timed out]"'],
                tty=True,
                stream=True,
                user='user',
                environment={
                    'TERM': 'xterm-256color',
                    'HOME': '/home/<USER>',
                    'PATH': '/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin',
                    'PWD': self.current_directory,
                    'USER': 'user',
                    'DEBIAN_FRONTEND': 'noninteractive'
                },
                workdir=self.current_directory
            )

            # Send initial message
            await self.send(text_data=json.dumps({
                'output': f'Starting: {command}\r\n'
            }))

            # Stream output in real-time
            output_buffer = ""
            for chunk in result:
                if chunk:
                    try:
                        decoded = chunk.decode('utf-8', errors='ignore')
                        output_buffer += decoded

                        # Send output in chunks
                        await self.send(text_data=json.dumps({
                            'output': decoded
                        }))

                        # Add small delay to prevent overwhelming
                        await asyncio.sleep(0.1)

                    except Exception as e:
                        print(f"DEBUG: Error processing chunk: {e}")
                        break

            # Send completion message
            await self.send(text_data=json.dumps({
                'output': f'\r\n[Command completed]\r\n$ '
            }))

            return ""  # Don't return additional output

        except Exception as e:
            print(f"DEBUG: Streaming command error: {e}")
            return f"Error executing streaming command: {str(e)}\r\n$ "

    async def execute_command(self, command):
        """Execute command in container asynchronously"""
        if not self.container:
            return 'Error: Container not available\r\n$ '

        # Debug: Log the original command type and value
        print(f"DEBUG: execute_command received: type={type(command)}, value={repr(command)}")

        # Ensure command is a string
        if not isinstance(command, str):
            print(f"DEBUG: Converting {type(command)} to string: {command}")
            command = str(command)

        command = command.strip()
        if not command:
            return '$ '

        loop = asyncio.get_event_loop()

        def _exec_command():
            try:
                # Handle cd command specially
                if command.startswith('cd '):
                    target_dir = command[3:].strip() or '/home/<USER>'
                    if target_dir.startswith('/'):
                        new_dir = target_dir
                    else:
                        new_dir = f'{self.current_directory}/{target_dir}'

                    # Normalize path
                    import os
                    new_dir = os.path.normpath(new_dir)

                    # Check if directory exists
                    check_result = self.container.exec_run(
                        cmd=['test', '-d', new_dir],
                        workdir='/home/<USER>'
                    )

                    if check_result.exit_code == 0:
                        self.current_directory = new_dir
                        return '$ '
                    else:
                        return f'cd: {target_dir}: No such file or directory\r\n$ '

                # Handle pwd command
                if command == 'pwd':
                    return f'{self.current_directory}\r\n$ '

                # Execute ALL commands using bash with full root privileges
                # No filtering - work like a real terminal
                command_str = str(command).strip()

                # Check for streaming commands that need special handling
                streaming_commands = ['runserver', 'npm start', 'npm run dev', 'yarn start', 'yarn dev', 'python -m http.server', 'serve', 'nodemon']
                is_streaming = any(cmd in command_str for cmd in streaming_commands)

                if is_streaming:
                    # Handle streaming commands with live output
                    asyncio.create_task(self.execute_streaming_command(command_str))
                    return f"Starting streaming command: {command_str}\r\n"
                else:
                    # Execute ALL other commands normally - no restrictions
                    result = self.container.exec_run(
                        cmd=['bash', '-c', command_str],
                        tty=True,
                        user='user',
                        environment={
                            'TERM': 'xterm-256color',
                            'HOME': '/home/<USER>',
                            'PATH': '/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin',
                            'PWD': self.current_directory,
                            'USER': 'user',
                            'DEBIAN_FRONTEND': 'noninteractive'
                        },
                        workdir=self.current_directory
                    )
                    output = result.output.decode('utf-8', errors='ignore')

                output = result.output.decode('utf-8', errors='ignore')

                # Clean up output - remove any trailing whitespace but preserve structure
                output = output.rstrip()

                # Format output nicely
                if output:
                    return f'{output}\r\n$ '
                else:
                    return '$ '

            except Exception as e:
                return f'Command failed: {str(e)}\r\n$ '

        return await loop.run_in_executor(None, _exec_command)

    async def install_nodejs(self):
        """Install Node.js in the container"""
        await self.send(text_data=json.dumps({
            'output': 'Installing Node.js... This may take a few minutes.\r\n'
        }))

        commands = [
            'apt update',
            'apt install -y nodejs npm',
            'node --version',
            'npm --version'
        ]

        for cmd in commands:
            await self.send(text_data=json.dumps({
                'output': f'Running: {cmd}\r\n'
            }))
            output = await self.execute_command(cmd)
            await self.send(text_data=json.dumps({'output': output}))

        await self.send(text_data=json.dumps({
            'output': '✅ Node.js installation complete!\r\n$ '
        }))

    async def setup_development_environment(self):
        """Setup complete development environment"""
        await self.send(text_data=json.dumps({
            'output': 'Setting up development environment... This may take several minutes.\r\n'
        }))

        commands = [
            'apt update',
            'apt install -y nodejs npm python3 python3-pip git vim nano curl wget',
            'npm install -g yarn typescript ts-node nodemon',
            'pip3 install flask django fastapi requests',
            'node --version',
            'npm --version',
            'python3 --version',
            'git --version'
        ]

        for cmd in commands:
            await self.send(text_data=json.dumps({
                'output': f'Running: {cmd}\r\n'
            }))
            output = await self.execute_command(cmd)
            await self.send(text_data=json.dumps({'output': output}))

        await self.send(text_data=json.dumps({
            'output': '✅ Development environment setup complete!\r\n$ '
        }))
