FROM ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV HOME=/home/<USER>
ENV PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/.local/bin

# Install essential development tools
RUN apt-get update && apt-get install -y \
    # Basic tools
    bash \
    curl \
    wget \
    git \
    vim \
    nano \
    tree \
    htop \
    unzip \
    zip \
    build-essential \
    sudo \
    # Programming languages
    python3 \
    python3-pip \
    python3-venv \
    python3-dev \
    software-properties-common \
    ca-certificates \
    gnupg \
    lsb-release \
    # Additional tools
    sqlite3 \
    # Network tools
    net-tools \
    iputils-ping \
    # File tools
    file \
    less \
    grep \
    sed \
    gawk \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 20 (LTS) - Required for modern tools like Vite
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs

# Install Java 17 (LTS) for Java development
RUN apt-get update && apt-get install -y \
    openjdk-17-jdk \
    maven \
    && rm -rf /var/lib/apt/lists/*

# Install global npm packages for modern development
RUN npm install -g \
    yarn \
    pnpm \
    typescript \
    ts-node \
    nodemon \
    create-react-app \
    @vue/cli \
    @angular/cli \
    vite \
    create-vite \
    create-next-app \
    express-generator

# Install Python packages for web development
RUN pip3 install \
    flask \
    django \
    fastapi \
    requests \
    black \
    flake8 \
    pytest \
    jupyter \
    pandas \
    numpy \
    django-rest-framework \
    celery \
    gunicorn \
    uvicorn

# Create user with proper shell and sudo access
RUN useradd -m -s /bin/bash user && \
    usermod -aG sudo user && \
    echo "user ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Create welcome files as root, then change ownership
RUN echo "Welcome to CodeForge Development Environment!" > /home/<USER>/welcome.txt && \
    echo "# CodeForge Projects" > /home/<USER>/README.md && \
    echo "This is your personal development container." >> /home/<USER>/README.md && \
    echo "" >> /home/<USER>/README.md && \
    echo "Available tools:" >> /home/<USER>/README.md && \
    echo "- Python 3 with Django, Flask, FastAPI" >> /home/<USER>/README.md && \
    echo "- Node.js 20 with npm, yarn, pnpm" >> /home/<USER>/README.md && \
    echo "- Java 17 with Maven" >> /home/<USER>/README.md && \
    echo "- Modern frameworks: React, Vue, Angular, Next.js" >> /home/<USER>/README.md && \
    echo "- Build tools: Vite, TypeScript, Webpack" >> /home/<USER>/README.md && \
    echo "- Git for version control" >> /home/<USER>/README.md && \
    echo "- Editors: Vim, Nano, Web Editor" >> /home/<USER>/README.md && \
    echo "" >> /home/<USER>/README.md && \
    echo "Quick start commands:" >> /home/<USER>/README.md && \
    echo "- npm create vite@latest my-app    # Create Vite project" >> /home/<USER>/README.md && \
    echo "- npm create react-app my-app     # Create React app" >> /home/<USER>/README.md && \
    echo "- django-admin startproject myapp # Create Django project" >> /home/<USER>/README.md && \
    echo "- python3 -m venv myenv           # Create Python virtual env" >> /home/<USER>/README.md && \
    echo "- mvn archetype:generate          # Create Java project" >> /home/<USER>/README.md && \
    mkdir -p /home/<USER>/projects && \
    chown -R user:user /home/<USER>

WORKDIR /home/<USER>
USER user

# Keep container running
CMD ["tail", "-f", "/dev/null"]