# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021
# This file is distributed under the same license as the Simple JWT package.
# <AUTHOR> <EMAIL>, 2021.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-06-23 13:29+0800\n"
"PO-Revision-Date: 2021-06-23 13:29+080\n"
"Last-Translator: zengqiu <<EMAIL>>\n"
"Language: zh_Hans\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr "授权头必须包含两个用空格分隔的值"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "此令牌对任何类型的令牌无效"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "令牌未包含用户标识符"

#: authentication.py:132
msgid "User not found"
msgstr "未找到该用户"

#: authentication.py:135
msgid "User is inactive"
msgstr "该用户已禁用"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "未知算法类型 '{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr "你必须安装 cryptography 才能使用 {}。"

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:125 backends.py:177 tokens.py:68
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "令牌无效或已过期"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr "指定的算法无效"

#: backends.py:175 tokens.py:66
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "令牌无效或已过期"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "令牌无效或已过期"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "找不到指定凭据对应的有效用户"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "找不到指定凭据对应的有效用户"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr "'{}' 配置已被移除。 请参阅 '{}' 获取可用的配置。"

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "用户"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "创建时间"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "过期时间"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "令牌黑名单"

#: tokens.py:52
msgid "Cannot create token with no type or lifetime"
msgstr "无法创建没有类型或生存期的令牌"

#: tokens.py:126
msgid "Token has no id"
msgstr "令牌没有标识符"

#: tokens.py:138
msgid "Token has no type"
msgstr "令牌没有类型"

#: tokens.py:141
msgid "Token has wrong type"
msgstr "令牌类型错误"

#: tokens.py:200
msgid "Token has no '{}' claim"
msgstr "令牌没有 '{}' 声明"

#: tokens.py:205
msgid "Token '{}' claim has expired"
msgstr "令牌 '{}' 声明已过期"

#: tokens.py:292
msgid "Token is blacklisted"
msgstr "令牌已被加入黑名单"
