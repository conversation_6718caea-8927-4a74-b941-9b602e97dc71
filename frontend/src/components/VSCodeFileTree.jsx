import { useState, useEffect } from 'react';
import { 
  FiFolder, 
  FiFolderOpen,
  FiFile, 
  FiChevronRight, 
  FiChevronDown, 
  FiPlus,
  FiRefreshCw,
  FiMoreHorizontal
} from 'react-icons/fi';
import { containerAPI } from '../services/api';

const VSCodeFileTree = ({ onFileSelect, currentFile }) => {
  const [treeData, setTreeData] = useState([]);
  const [expandedFolders, setExpandedFolders] = useState(new Set(['/home/<USER>']));
  const [loading, setLoading] = useState(false);
  const [contextMenu, setContextMenu] = useState(null);

  useEffect(() => {
    loadFileTree();

    // Add keyboard shortcuts
    const handleKeyDown = (e) => {
      if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        createNewFile();
      }
      if (e.ctrlKey && e.shiftKey && e.key === 'N') {
        e.preventDefault();
        createNewFolder();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const getFolderIcon = (folderName) => {
    const folderIconMap = {
      'src': '📁',
      'components': '🧩',
      'pages': '📄',
      'utils': '🔧',
      'assets': '🖼️',
      'images': '🖼️',
      'styles': '🎨',
      'css': '🎨',
      'js': '📜',
      'api': '🌐',
      'services': '⚙️',
      'hooks': '🪝',
      'context': '🔗',
      'lib': '📚',
      'public': '🌍',
      'build': '🏗️',
      'dist': '📦',
      'node_modules': '📦',
      'tests': '🧪',
      'test': '🧪',
      '__tests__': '🧪',
      'docs': '📖',
      'config': '⚙️',
      '.git': '🔀',
      '.vscode': '💙',
    };
    return folderIconMap[folderName.toLowerCase()] || null;
  };

  const getFileIcon = (fileName, isFolder = false) => {
    if (isFolder) return getFolderIcon(fileName);
    
    const ext = fileName.split('.').pop()?.toLowerCase();
    const iconMap = {
      // JavaScript/TypeScript
      'js': { icon: '📄', color: '#f7df1e' },
      'jsx': { icon: '⚛️', color: '#61dafb' },
      'ts': { icon: '📘', color: '#3178c6' },
      'tsx': { icon: '⚛️', color: '#61dafb' },
      
      // Web
      'html': { icon: '🌐', color: '#e34f26' },
      'css': { icon: '🎨', color: '#1572b6' },
      'scss': { icon: '🎨', color: '#cf649a' },
      'sass': { icon: '🎨', color: '#cf649a' },
      
      // Data
      'json': { icon: '📋', color: '#000000' },
      'xml': { icon: '📄', color: '#ff6600' },
      'yml': { icon: '📄', color: '#cb171e' },
      'yaml': { icon: '📄', color: '#cb171e' },
      
      // Documentation
      'md': { icon: '📝', color: '#000000' },
      'txt': { icon: '📄', color: '#000000' },
      'pdf': { icon: '📕', color: '#ff0000' },
      
      // Programming Languages
      'py': { icon: '🐍', color: '#3776ab' },
      'java': { icon: '☕', color: '#ed8b00' },
      'cpp': { icon: '⚙️', color: '#00599c' },
      'c': { icon: '⚙️', color: '#a8b9cc' },
      'php': { icon: '🐘', color: '#777bb4' },
      'rb': { icon: '💎', color: '#cc342d' },
      'go': { icon: '🐹', color: '#00add8' },
      'rs': { icon: '🦀', color: '#000000' },
      
      // Frameworks
      'vue': { icon: '💚', color: '#4fc08d' },
      'svelte': { icon: '🧡', color: '#ff3e00' },
      
      // Images
      'png': { icon: '🖼️', color: '#000000' },
      'jpg': { icon: '🖼️', color: '#000000' },
      'jpeg': { icon: '🖼️', color: '#000000' },
      'gif': { icon: '🖼️', color: '#000000' },
      'svg': { icon: '🎨', color: '#ffb13b' },
      
      // Config
      'env': { icon: '🔧', color: '#faf047' },
      'gitignore': { icon: '🚫', color: '#f14e32' },
      'dockerfile': { icon: '🐳', color: '#2496ed' },
      'lock': { icon: '🔒', color: '#000000' },
      'config': { icon: '⚙️', color: '#6c6c6c' },
    };
    
    return iconMap[ext] || { icon: '📄', color: '#6c6c6c' };
  };

  const loadFileTree = async () => {
    setLoading(true);
    try {
      // Get complete directory structure using find
      const result = await containerAPI.executeCommand('find /home/<USER>');
      if (result.success) {
        const output = result.data.output;
        const paths = output.split('\n')
          .filter(line => line.trim() && !line.includes('$'))
          .map(line => line.trim())
          .filter(p => p && !p.includes('/.') && p !== '/home/<USER>'); // Filter hidden files
        
        const tree = buildVSCodeTree(paths);
        setTreeData(tree);
      }
    } catch (error) {
      console.error('Failed to load file tree:', error);
      setTreeData([]);
    } finally {
      setLoading(false);
    }
  };

  const buildVSCodeTree = (paths) => {
    const tree = {};
    const basePath = '/home/<USER>';
    
    paths.forEach(fullPath => {
      if (!fullPath.startsWith(basePath)) return;
      
      const relativePath = fullPath.replace(basePath + '/', '');
      if (!relativePath) return;
      
      const parts = relativePath.split('/');
      let current = tree;
      
      parts.forEach((part, index) => {
        const currentPath = basePath + '/' + parts.slice(0, index + 1).join('/');
        const isFile = index === parts.length - 1 && !paths.some(p => p.startsWith(currentPath + '/'));
        
        if (!current[part]) {
          current[part] = {
            name: part,
            path: currentPath,
            type: isFile ? 'file' : 'folder',
            children: {},
            level: index
          };
        }
        current = current[part].children;
      });
    });
    
    return convertToArray(tree);
  };

  const convertToArray = (obj) => {
    const items = Object.values(obj).map(item => ({
      ...item,
      children: convertToArray(item.children)
    }));
    
    // Sort: folders first, then files, both alphabetically
    return items.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'folder' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });
  };

  const toggleFolder = (path) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedFolders(newExpanded);
  };

  const handleItemClick = (item) => {
    if (item.type === 'folder') {
      toggleFolder(item.path);
    } else {
      onFileSelect(item);
    }
  };

  const handleContextMenu = (e, item) => {
    e.preventDefault();
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      item
    });
  };

  const createNewFile = async () => {
    const fileName = prompt('Enter file name:');
    if (fileName && fileName.trim()) {
      try {
        const content = `// ${fileName}\n`;
        const encodedContent = btoa(content);
        const result = await containerAPI.executeCommand(`echo '${encodedContent}' | base64 -d > /home/<USER>/${fileName.trim()}`);
        if (result.success) {
          loadFileTree();
          setTimeout(() => {
            const newFile = { name: fileName.trim(), path: `/home/<USER>/${fileName.trim()}`, type: 'file' };
            onFileSelect(newFile);
          }, 500);
        }
      } catch (error) {
        alert('Failed to create file: ' + error.message);
      }
    }
    setContextMenu(null);
  };

  const createNewFolder = async () => {
    const folderName = prompt('Enter folder name:');
    if (folderName && folderName.trim()) {
      try {
        const result = await containerAPI.executeCommand(`mkdir -p /home/<USER>/${folderName.trim()}`);
        if (result.success) {
          loadFileTree();
        }
      } catch (error) {
        alert('Failed to create folder: ' + error.message);
      }
    }
    setContextMenu(null);
  };

  const renderTreeItem = (item, depth = 0) => {
    const isExpanded = expandedFolders.has(item.path);
    const isSelected = currentFile?.path === item.path;
    const fileIcon = getFileIcon(item.name, item.type === 'folder');
    
    return (
      <div key={item.path}>
        <div
          className={`flex items-center h-7 cursor-pointer text-sm group relative ${
            isSelected
              ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-l-2 border-blue-500'
              : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
          }`}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
          onClick={() => handleItemClick(item)}
          onContextMenu={(e) => handleContextMenu(e, item)}
        >
          {/* Expand/Collapse Icon */}
          {item.type === 'folder' && (
            <div className="w-4 h-4 flex items-center justify-center mr-1 flex-shrink-0">
              {isExpanded ? (
                <FiChevronDown size={10} className="text-gray-500" />
              ) : (
                <FiChevronRight size={10} className="text-gray-500" />
              )}
            </div>
          )}
          {item.type === 'file' && <div className="w-5 mr-1 flex-shrink-0"></div>}

          {/* Folder/File Icon */}
          <div className="w-4 h-4 flex items-center justify-center mr-2 flex-shrink-0">
            {item.type === 'folder' ? (
              fileIcon ? (
                <span className="text-xs leading-none">{fileIcon}</span>
              ) : isExpanded ? (
                <FaFolderOpen size={14} className="text-blue-600 dark:text-blue-400" />
              ) : (
                <FiFolder size={14} className="text-blue-600 dark:text-blue-400" />
              )
            ) : (
              <span className="text-xs leading-none">{fileIcon.icon}</span>
            )}
          </div>
          
          {/* File/Folder Name */}
          <span className="flex-1 truncate">{item.name}</span>
          
          {/* Hover Actions */}
          <div className="opacity-0 group-hover:opacity-100 flex items-center ml-1">
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleContextMenu(e, item);
              }}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
            >
              <FiMoreHorizontal size={12} />
            </button>
          </div>
        </div>
        
        {/* Render Children */}
        {item.type === 'folder' && isExpanded && item.children && (
          <div>
            {item.children.map(child => renderTreeItem(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-900 text-sm">
      {/* Header */}
      <div className="flex items-center justify-between px-3 py-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center space-x-2">
          <FiFolder size={14} className="text-blue-500" />
          <span className="text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wide">
            Explorer
          </span>
        </div>
        <div className="flex items-center space-x-1">
          <button
            onClick={createNewFile}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded opacity-70 hover:opacity-100"
            title="New File (Ctrl+N)"
          >
            <FiPlus size={12} />
          </button>
          <button
            onClick={createNewFolder}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded opacity-70 hover:opacity-100"
            title="New Folder (Ctrl+Shift+N)"
          >
            <FiFolder size={12} />
          </button>
          <button
            onClick={loadFileTree}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded opacity-70 hover:opacity-100"
            title="Refresh Explorer"
          >
            <FiRefreshCw size={12} className={loading ? 'animate-spin' : ''} />
          </button>
        </div>
      </div>

      {/* Workspace Section */}
      <div className="px-3 py-1 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
          <FiChevronDown size={12} className="mr-1" />
          <span className="font-medium">CODEFORGE</span>
        </div>
      </div>
      
      {/* File Tree */}
      <div className="flex-1 overflow-auto">
        {loading ? (
          <div className="p-4 text-center text-gray-500">
            <FiRefreshCw className="animate-spin mx-auto mb-2" size={16} />
            <p className="text-xs">Loading...</p>
          </div>
        ) : treeData.length > 0 ? (
          <div className="py-1">
            {treeData.map(item => renderTreeItem(item))}
          </div>
        ) : (
          <div className="p-4 text-center text-gray-500">
            <p className="text-xs mb-2">No files found</p>
            <button
              onClick={createNewFile}
              className="text-xs text-blue-500 hover:text-blue-600"
            >
              Create your first file
            </button>
          </div>
        )}
      </div>
      
      {/* Context Menu */}
      {contextMenu && (
        <>
          <div
            className="fixed inset-0 z-40"
            onClick={() => setContextMenu(null)}
          />
          <div
            className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg py-1 min-w-32"
            style={{
              left: contextMenu.x,
              top: contextMenu.y,
            }}
          >
            <button
              onClick={createNewFile}
              className="block w-full text-left px-3 py-1.5 text-xs hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              New File
            </button>
            <button
              onClick={createNewFolder}
              className="block w-full text-left px-3 py-1.5 text-xs hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              New Folder
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default VSCodeFileTree;
