
import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  FiSave,
  FiPlay,
  FiTerminal,
  FiMessageSquare,
  FiX,
  FiMenu,
  FiMoon,
  FiSun,
  FiFolder
} from "react-icons/fi";
import Editor from "@monaco-editor/react";
import { Terminal } from "@xterm/xterm";
import { FitAddon } from "@xterm/addon-fit";
import "@xterm/xterm/css/xterm.css";
import { useAuth } from "../contexts/AuthContext";
import { containerAPI, TerminalWebSocket } from "../services/api";
import VSCodeFileTree from "../components/VSCodeFileTree";

export default function EditorPage() {
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("editor");
  const [darkMode, setDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      const savedMode = localStorage.getItem('darkMode');
      return savedMode ? JSON.parse(savedMode) :
        window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return true;
  });
  const [containerStatus, setContainerStatus] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [code, setCode] = useState(`// Welcome to CodeForge Editor
// Your files will be loaded from your container
// Create new files using the file explorer

console.log("Welcome to CodeForge!");`);
  const [language, setLanguage] = useState("javascript");
  const [fileName, setFileName] = useState("welcome.js");
  const [currentFile, setCurrentFile] = useState(null);
  const [isFileExplorerOpen, setIsFileExplorerOpen] = useState(true);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [lastSaved, setLastSaved] = useState(null);
  const autoSaveTimeoutRef = useRef(null);
  const terminalRef = useRef(null);
  const xtermRef = useRef(null);
  const fitAddonRef = useRef(null);
  const wsRef = useRef(null);
  const [terminalOutput, setTerminalOutput] = useState('');
  // Check authentication on component mount
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    // Check container status
    checkContainerStatus();
  }, [isAuthenticated, navigate]);

  const checkContainerStatus = async () => {
    const result = await containerAPI.getStatus();
    if (result.success) {
      setContainerStatus(result.data);
    } else {
      console.error('Failed to get container status:', result.error);
    }
  };

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  const handleExit = () => {
    if (window.confirm("Are you sure you want to exit the editor? Any unsaved changes will be lost.")) {
      // Disconnect WebSocket before leaving
      if (wsRef.current) {
        wsRef.current.disconnect();
      }
      navigate('/');
    }
  };

  const handleFileSelect = async (file) => {
    if (unsavedChanges) {
      if (!confirm('You have unsaved changes. Do you want to continue?')) {
        return;
      }
    }

    try {
      // Load file content from container
      // Use the full path directly since file.path already contains the complete path
      const filePath = file.path.startsWith('/') ? file.path : `/home/<USER>/${file.path}`;
      console.log('Loading file:', filePath);

      const result = await containerAPI.executeCommand(`cat "${filePath}"`);
      if (result.success) {
        const content = result.data.output.replace(/\$ $/, '').trim();
        setCode(content);
        setFileName(file.name);
        setCurrentFile(file);
        setLanguage(getLanguageFromExtension(file.name));
        setUnsavedChanges(false);
      } else {
        console.error('Failed to load file:', result);
        alert('Failed to load file: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Failed to load file:', error);
      alert('Failed to load file');
    }
  };

  const getLanguageFromExtension = (filename) => {
    const ext = filename.split('.').pop().toLowerCase();
    const languageMap = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'html': 'html',
      'css': 'css',
      'json': 'json',
      'md': 'markdown',
      'cpp': 'cpp',
      'c': 'c',
      'java': 'java',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'sh': 'shell',
      'yml': 'yaml',
      'yaml': 'yaml',
      'xml': 'xml',
      'sql': 'sql'
    };
    return languageMap[ext] || 'plaintext';
  };

  const handleSave = async () => {
    if (!currentFile) {
      // Create new file
      const newFileName = prompt('Enter filename:', fileName);
      if (!newFileName) return;

      try {
        // Create file with content using base64 encoding
        const encodedContent = btoa(code);
        const fullPath = `/home/<USER>/${newFileName}`;
        console.log('Creating new file at:', fullPath);

        const result = await containerAPI.executeCommand(`echo '${encodedContent}' | base64 -d > "${fullPath}"`);
        if (result.success) {
          setFileName(newFileName);
          setCurrentFile({ name: newFileName, path: fullPath, type: 'file' });
          setUnsavedChanges(false);
          console.log('File created and saved successfully!');
          // Show success message briefly
          const originalTitle = document.title;
          document.title = '✅ Created - ' + originalTitle;
          setTimeout(() => { document.title = originalTitle; }, 2000);
        } else {
          console.error('Failed to create file:', result);
          alert('Failed to create file: ' + (result.error || 'Unknown error'));
        }
      } catch (error) {
        console.error('Failed to create file:', error);
        alert('Failed to create file');
      }
    } else {
      // Save existing file
      try {
        // Use a more reliable method to save files
        const filePath = currentFile.path.startsWith('/') ? currentFile.path : `/home/<USER>/${currentFile.path}`;
        console.log('Saving file to:', filePath);
        console.log('File content length:', code.length);

        // Try multiple methods to save the file
        let result;

        // Method 1: Try with base64 encoding for special characters
        try {
          const encodedContent = btoa(unescape(encodeURIComponent(code)));
          result = await containerAPI.executeCommand(`echo '${encodedContent}' | base64 -d > "${filePath}"`);
          console.log('Base64 save result:', result);
        } catch (base64Error) {
          console.log('Base64 method failed, trying direct write:', base64Error);

          // Method 2: Direct write with escaped content
          const escapedContent = code.replace(/'/g, "'\"'\"'").replace(/\$/g, '\\$').replace(/`/g, '\\`');
          result = await containerAPI.executeCommand(`cat > "${filePath}" << 'EOF'\n${code}\nEOF`);
          console.log('Direct write result:', result);
        }

        if (result && result.success) {
          setUnsavedChanges(false);
          console.log('File saved successfully!');

          // Verify the file was saved by reading it back
          const verifyResult = await containerAPI.executeCommand(`cat "${filePath}"`);
          if (verifyResult.success) {
            console.log('File verification successful');
          } else {
            console.warn('File verification failed:', verifyResult);
          }

          // Show success message briefly
          const originalTitle = document.title;
          document.title = '✅ Saved - ' + originalTitle;
          setTimeout(() => { document.title = originalTitle; }, 2000);
        } else {
          console.error('Failed to save file:', result);
          alert('Failed to save file: ' + (result?.error || result?.message || 'Unknown error'));
        }
      } catch (error) {
        console.error('Failed to save file:', error);
        alert('Failed to save file: ' + error.message);
      }
    }
  };

  const autoSave = async () => {
    if (!currentFile || !unsavedChanges) return;

    try {
      const filePath = currentFile.path.startsWith('/') ? currentFile.path : `/home/<USER>/${currentFile.path}`;
      console.log('Auto-saving file to:', filePath);

      // Use the same reliable saving method as manual save
      let result;

      try {
        const encodedContent = btoa(unescape(encodeURIComponent(code)));
        result = await containerAPI.executeCommand(`echo '${encodedContent}' | base64 -d > "${filePath}"`);
      } catch (base64Error) {
        // Fallback to direct write
        result = await containerAPI.executeCommand(`cat > "${filePath}" << 'EOF'\n${code}\nEOF`);
      }

      if (result && result.success) {
        setUnsavedChanges(false);
        setLastSaved(new Date());
        console.log('Auto-saved:', currentFile.name);
      } else {
        console.error('Auto-save failed:', result);
      }
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  };

  const handleCodeChange = (value) => {
    setCode(value || "");
    setUnsavedChanges(true);

    // Clear existing auto-save timeout
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    // Set new auto-save timeout (2 seconds after user stops typing)
    if (autoSaveEnabled && currentFile) {
      autoSaveTimeoutRef.current = setTimeout(() => {
        autoSave();
      }, 2000);
    }
  };

  // Cleanup auto-save timeout on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  const handleRun = async () => {
    if (activeTab !== "terminal") {
      setActiveTab("terminal");
    }

    // Execute code in backend container
    if (language === "javascript") {
      // Try multiple Node.js execution methods
      const commands = [
        `node -e "${code.replace(/"/g, '\\"')}"`,
        `nodejs -e "${code.replace(/"/g, '\\"')}"`,
        `echo '${code.replace(/'/g, "'\\''")}' > temp_script.js && node temp_script.js && rm temp_script.js`,
        `echo '${code.replace(/'/g, "'\\''")}' > temp_script.js && nodejs temp_script.js && rm temp_script.js`
      ];

      let executed = false;
      for (const command of commands) {
        const result = await containerAPI.executeCommand(command);
        if (result.success && xtermRef.current) {
          xtermRef.current.write(`\r\n$ ${command}\r\n`);
          xtermRef.current.write(result.data.output);
          xtermRef.current.write("$ ");
          executed = true;
          break;
        }
      }

      if (!executed && xtermRef.current) {
        xtermRef.current.write(`\r\n$ # Node.js not available in container\r\n`);
        xtermRef.current.write(`# Please install Node.js first: apt update && apt install -y nodejs npm\r\n`);
        xtermRef.current.write("$ ");
      }
    } else if (language === "python") {
      const command = `python3 -c "${code.replace(/"/g, '\\"')}"`;
      const result = await containerAPI.executeCommand(command);

      if (result.success && xtermRef.current) {
        xtermRef.current.write(`\r\n$ ${command}\r\n`);
        xtermRef.current.write(result.data.output);
        xtermRef.current.write("$ ");
      }
    } else {
      // For other languages, just show in terminal
      if (xtermRef.current) {
        xtermRef.current.write(`\r\n$ Running ${fileName}...\r\n`);
        xtermRef.current.write("Code execution not implemented for this language yet.\r\n");
        xtermRef.current.write("$ ");
      }
    }
  };

  // Initialize terminal when terminal tab is active
  useEffect(() => {
    if (activeTab === "terminal" && terminalRef.current && !xtermRef.current && isAuthenticated) {
      setIsConnecting(true);

      // Initialize xterm.js terminal
      xtermRef.current = new Terminal({
        theme: {
          background: darkMode ? '#1a1a1a' : '#ffffff',
          foreground: darkMode ? '#ffffff' : '#000000',
          cursor: '#00ff00',
          selection: 'rgba(255, 255, 255, 0.3)',
        },
        fontSize: 14,
        fontFamily: 'JetBrains Mono, monospace',
        cursorBlink: true,
        cols: 80,
        rows: 24,
        convertEol: true,
        disableStdin: false,
        cursorStyle: 'block',
      });

      fitAddonRef.current = new FitAddon();
      xtermRef.current.loadAddon(fitAddonRef.current);
      xtermRef.current.open(terminalRef.current);
      fitAddonRef.current.fit();

      // Initialize WebSocket connection
      wsRef.current = new TerminalWebSocket(
        (output) => {
          if (xtermRef.current) {
            xtermRef.current.write(output);
          }
          setTerminalOutput(prev => prev + output);
          setIsConnecting(false); // Connection is working if we receive data
        },
        (error) => {
          console.error('Terminal WebSocket error:', error);
          if (xtermRef.current) {
            xtermRef.current.write(`\r\n\x1b[31mWebSocket Error: ${error}\x1b[0m\r\n`);
          }
          setIsConnecting(false);
        },
        () => {
          console.log('Terminal WebSocket disconnected');
          if (xtermRef.current) {
            xtermRef.current.write('\r\n\x1b[33mConnection closed\x1b[0m\r\n');
          }
          setIsConnecting(false);
        }
      );

      // Connect to backend terminal
      try {
        wsRef.current.connect();
      } catch (error) {
        console.error('Failed to connect to terminal:', error);
        setIsConnecting(false);
        if (xtermRef.current) {
          xtermRef.current.write('\r\n\x1b[31mFailed to connect to terminal\x1b[0m\r\n');
        }
      }

      // Handle terminal input
      xtermRef.current.onData((data) => {
        if (wsRef.current) {
          wsRef.current.sendRawInput(data);
        }
      });

      setIsConnecting(false);
    }

    return () => {
      if (xtermRef.current) {
        xtermRef.current.dispose();
        xtermRef.current = null;
        fitAddonRef.current = null;
      }
      if (wsRef.current) {
        wsRef.current.disconnect();
        wsRef.current = null;
      }
    };
  }, [activeTab, darkMode, isAuthenticated]);

  // Handle window resize for terminal
  useEffect(() => {
    const handleResize = () => {
      if (fitAddonRef.current && xtermRef.current) {
        fitAddonRef.current.fit();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);



  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white font-mono flex flex-col">
      {/* Top Navigation Bar */}
      <nav className="bg-gray-800 dark:bg-gray-900 text-white px-4 py-2 flex items-center justify-between border-b border-gray-700">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="p-2 hover:bg-gray-700 rounded"
          >
            <FiMenu />
          </button>
          <h1 className="text-lg font-bold">CodeForge Editor</h1>
        </div>

        <div className="flex items-center space-x-2">
          {/* User Info */}
          {user && (
            <span className="text-sm text-gray-300 mr-2">
              Welcome, {user.username}
            </span>
          )}

          {/* Container Status */}
          {containerStatus && (
            <div className={`px-2 py-1 rounded text-xs ${
              containerStatus.status === 'running'
                ? 'bg-green-600 text-white'
                : 'bg-yellow-600 text-black'
            }`}>
              Container: {containerStatus.status}
            </div>
          )}

          {/* Auto-save Status */}
          {autoSaveEnabled && lastSaved && (
            <div className="px-2 py-1 rounded text-xs bg-gray-600 text-white">
              Auto-saved: {lastSaved.toLocaleTimeString()}
            </div>
          )}

          <button
            onClick={handleSave}
            className="flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm"
          >
            <FiSave />
            <span>Save</span>
          </button>
          <button
            onClick={() => setAutoSaveEnabled(!autoSaveEnabled)}
            className={`flex items-center space-x-1 px-3 py-1 rounded text-sm ${
              autoSaveEnabled
                ? 'bg-green-600 hover:bg-green-700'
                : 'bg-gray-600 hover:bg-gray-700'
            }`}
            title={autoSaveEnabled ? 'Auto-save enabled' : 'Auto-save disabled'}
          >
            <span className="text-xs">⚡</span>
            <span>{autoSaveEnabled ? 'Auto' : 'Manual'}</span>
          </button>
          <button
            onClick={handleRun}
            className="flex items-center space-x-1 px-3 py-1 bg-green-600 hover:bg-green-700 rounded text-sm"
          >
            <FiPlay />
            <span>Run</span>
          </button>
          <button
            onClick={() => setActiveTab(activeTab === "terminal" ? "editor" : "terminal")}
            className={`flex items-center space-x-1 px-3 py-1 rounded text-sm ${
              activeTab === "terminal" ? "bg-yellow-600" : "bg-gray-600 hover:bg-gray-700"
            }`}
          >
            <FiTerminal />
            <span>{isConnecting ? 'Connecting...' : 'Terminal'}</span>
          </button>
          <button
            onClick={() => setActiveTab(activeTab === "chat" ? "editor" : "chat")}
            className={`flex items-center space-x-1 px-3 py-1 rounded text-sm ${
              activeTab === "chat" ? "bg-purple-600" : "bg-gray-600 hover:bg-gray-700"
            }`}
          >
            <FiMessageSquare />
            <span>Chat</span>
          </button>
          <button
            onClick={toggleDarkMode}
            className="p-2 hover:bg-gray-700 rounded"
          >
            {darkMode ? <FiSun /> : <FiMoon />}
          </button>
          <button
            onClick={handleExit}
            className="p-2 hover:bg-red-600 rounded text-red-400"
          >
            <FiX />
          </button>
        </div>
      </nav>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* File Explorer */}
        {isFileExplorerOpen && (
          <div className="w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col">
            <VSCodeFileTree
              onFileSelect={handleFileSelect}
              currentFile={currentFile}
            />
          </div>
        )}

        {/* Toggle File Explorer Button */}
        {!isFileExplorerOpen && (
          <button
            onClick={() => setIsFileExplorerOpen(true)}
            className="w-8 bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 border-r border-gray-400 dark:border-gray-600 flex items-center justify-center"
          >
            <FiFolder className="text-sm" />
          </button>
        )}

        {/* Editor Pane */}
        <div className={`${activeTab === "editor" ? "flex-1" : "flex-1"} overflow-hidden flex flex-col`}>
          <div className="bg-gray-200 dark:bg-gray-700 border-b border-gray-300 dark:border-gray-700 px-4 py-1 flex">
            <div className="px-3 py-1 bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 rounded-t border-t border-l border-r border-gray-300 dark:border-gray-600 flex items-center">
              <span>{fileName}</span>
              {unsavedChanges && (
                <span className="ml-2 w-2 h-2 bg-orange-500 rounded-full" title="Unsaved changes"></span>
              )}
            </div>
          </div>
          <div className="flex-1">
            <Editor
              height="100%"
              language={language}
              value={code}
              onChange={handleCodeChange}
              theme={darkMode ? "vs-dark" : "light"}
              options={{
                fontSize: 14,
                fontFamily: 'JetBrains Mono, monospace',
                minimap: { enabled: true },
                scrollBeyondLastLine: false,
                automaticLayout: true,
                tabSize: 2,
                insertSpaces: true,
                wordWrap: 'on',
              }}
            />
          </div>
        </div>

        {/* Terminal or Chat Pane */}
        {activeTab !== "editor" && (
          <div className="w-80 border-l border-gray-300 dark:border-gray-700 flex flex-col">
            {activeTab === "terminal" ? (
              <>
                <div className="bg-gray-200 dark:bg-gray-700 border-b border-gray-300 dark:border-gray-700 px-4 py-2 flex items-center justify-between">
                  <span className="font-semibold text-sm">Terminal</span>
                  <button
                    onClick={() => setActiveTab("editor")}
                    className="p-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded"
                  >
                    <FiX className="text-xs" />
                  </button>
                </div>
                <div ref={terminalRef} className="flex-1 bg-black" />
              </>
            ) : (
              <>
                <div className="bg-gray-200 dark:bg-gray-700 border-b border-gray-300 dark:border-gray-700 px-4 py-2 flex items-center justify-between">
                  <span className="font-semibold text-sm">Chat</span>
                  <button
                    onClick={() => setActiveTab("editor")}
                    className="p-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded"
                  >
                    <FiX className="text-xs" />
                  </button>
                </div>
                <div className="flex-1 overflow-auto p-4 bg-gray-50 dark:bg-gray-800 text-sm">
                  <div className="text-center text-gray-500 dark:text-gray-400 mt-8">
                    <FiMessageSquare className="mx-auto text-3xl mb-2" />
                    <p>Chat feature coming soon!</p>
                    <p className="text-xs mt-1">Connect with your team in real-time</p>
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
