import { useState, useEffect } from 'react';
import {
  FiFolder,
  FiFile,
  FiChevronRight,
  FiChevronDown,
  FiPlus,
  FiRefreshCw,
  FiEdit3,
  FiTrash2,
  FiMoreHorizontal
} from 'react-icons/fi';
import { containerAPI } from '../services/api';

const VSCodeFileTree = ({ onFileSelect, currentFile }) => {
  const [treeData, setTreeData] = useState([]);
  const [expandedFolders, setExpandedFolders] = useState(new Set(['/home/<USER>']));
  const [loadingFolders, setLoadingFolders] = useState(new Set());
  const [loading, setLoading] = useState(false);
  const [contextMenu, setContextMenu] = useState(null);

  useEffect(() => {
    // Load file tree only once on mount
    loadFileTree();
  }, []);

  useEffect(() => {
    // Add keyboard shortcuts
    const handleKeyDown = (e) => {
      if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        createNewFile();
      }
      if (e.ctrlKey && e.shiftKey && e.key === 'N') {
        e.preventDefault();
        createNewFolder();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const getFolderIcon = (folderName) => {
    const folderIconMap = {
      'src': '📁',
      'components': '🧩',
      'pages': '📄',
      'utils': '🔧',
      'assets': '🖼️',
      'images': '🖼️',
      'styles': '🎨',
      'css': '🎨',
      'js': '📜',
      'api': '🌐',
      'services': '⚙️',
      'hooks': '🪝',
      'context': '🔗',
      'lib': '📚',
      'public': '🌍',
      'build': '🏗️',
      'dist': '📦',
      'node_modules': '📦',
      'tests': '🧪',
      'test': '🧪',
      '__tests__': '🧪',
      'docs': '📖',
      'config': '⚙️',
      '.git': '🔀',
      '.vscode': '💙',
    };
    return folderIconMap[folderName.toLowerCase()] || null;
  };

  const getFileIcon = (fileName, isFolder = false) => {
    if (isFolder) return getFolderIcon(fileName);
    
    const ext = fileName.split('.').pop()?.toLowerCase();
    const iconMap = {
      // JavaScript/TypeScript
      'js': { icon: '📄', color: '#f7df1e' },
      'jsx': { icon: '⚛️', color: '#61dafb' },
      'ts': { icon: '📘', color: '#3178c6' },
      'tsx': { icon: '⚛️', color: '#61dafb' },
      
      // Web
      'html': { icon: '🌐', color: '#e34f26' },
      'css': { icon: '🎨', color: '#1572b6' },
      'scss': { icon: '🎨', color: '#cf649a' },
      'sass': { icon: '🎨', color: '#cf649a' },
      
      // Data
      'json': { icon: '📋', color: '#000000' },
      'xml': { icon: '📄', color: '#ff6600' },
      'yml': { icon: '📄', color: '#cb171e' },
      'yaml': { icon: '📄', color: '#cb171e' },
      
      // Documentation
      'md': { icon: '📝', color: '#000000' },
      'txt': { icon: '📄', color: '#000000' },
      'pdf': { icon: '📕', color: '#ff0000' },
      
      // Programming Languages
      'py': { icon: '🐍', color: '#3776ab' },
      'java': { icon: '☕', color: '#ed8b00' },
      'cpp': { icon: '⚙️', color: '#00599c' },
      'c': { icon: '⚙️', color: '#a8b9cc' },
      'php': { icon: '🐘', color: '#777bb4' },
      'rb': { icon: '💎', color: '#cc342d' },
      'go': { icon: '🐹', color: '#00add8' },
      'rs': { icon: '🦀', color: '#000000' },
      
      // Frameworks
      'vue': { icon: '💚', color: '#4fc08d' },
      'svelte': { icon: '🧡', color: '#ff3e00' },
      
      // Images
      'png': { icon: '🖼️', color: '#000000' },
      'jpg': { icon: '🖼️', color: '#000000' },
      'jpeg': { icon: '🖼️', color: '#000000' },
      'gif': { icon: '🖼️', color: '#000000' },
      'svg': { icon: '🎨', color: '#ffb13b' },
      
      // Config
      'env': { icon: '🔧', color: '#faf047' },
      'gitignore': { icon: '🚫', color: '#f14e32' },
      'dockerfile': { icon: '🐳', color: '#2496ed' },
      'lock': { icon: '🔒', color: '#000000' },
      'config': { icon: '⚙️', color: '#6c6c6c' },
    };
    
    return iconMap[ext] || { icon: '📄', color: '#6c6c6c' };
  };

  const loadFileTree = async () => {
    setLoading(true);

    // Clear existing data first to prevent duplicates
    setTreeData([]);

    try {
      console.log('Loading file tree...');

      // Try to get directory listing
      let result = await containerAPI.executeCommand('ls -la /home/<USER>/dev/null || (mkdir -p /home/<USER>/home/<USER>');

      if (result.success && result.data && result.data.output) {
        const output = result.data.output;
        console.log('Raw directory output:', output);

        // Check if directory is empty or doesn't exist
        if (output.includes('No such file') ||
            output.includes('cannot access') ||
            output.split('\n').filter(line => line.match(/^[drwx-]{10}/) && !line.endsWith(' .') && !line.endsWith(' ..')).length === 0) {

          console.log('Directory empty or not found, creating sample files...');
          await createSampleFiles();

          // Get fresh listing after creating files
          result = await containerAPI.executeCommand('ls -la /home/<USER>');
          if (result.success && result.data) {
            const tree = parseDirectoryListing(result.data.output);
            setTreeData(tree);
          } else {
            setTreeData(getSampleTree());
          }
        } else {
          // Parse existing directory
          const tree = parseDirectoryListing(output);
          setTreeData(tree);
        }
      } else {
        console.error('Failed to get directory listing:', result);
        setTreeData(getSampleTree());
      }
    } catch (error) {
      console.error('Error loading file tree:', error);
      setTreeData(getSampleTree());
    } finally {
      setLoading(false);
    }
  };

  const parseDirectoryListing = (output, basePath = '/home/<USER>') => {
    console.log('Parsing directory listing:', output, 'basePath:', basePath);

    const lines = output.split('\n').filter(line => {
      const trimmed = line.trim();
      return trimmed &&
             !trimmed.includes('$') &&
             !trimmed.startsWith('total') &&
             trimmed.match(/^[drwx-]{10}/); // Only lines that start with permissions
    });

    console.log('Filtered lines:', lines);

    const items = [];
    const seenNames = new Set(); // Prevent duplicates

    lines.forEach(line => {
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 9) {
        const permissions = parts[0];
        const name = parts.slice(8).join(' ');

        // Filter out current dir, parent dir, hidden files, and duplicates
        if (name &&
            name !== '.' &&
            name !== '..' &&
            !name.startsWith('.') &&
            !seenNames.has(name)) {

          seenNames.add(name);
          const isDirectory = permissions.startsWith('d');

          items.push({
            name: name,
            path: `${basePath}/${name}`,
            type: isDirectory ? 'folder' : 'file',
            children: [],
            level: 0
          });
        }
      }
    });

    console.log('Parsed items:', items);

    // Sort: folders first, then files
    const sortedItems = items.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'folder' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });

    console.log('Final sorted items:', sortedItems);
    return sortedItems;
  };

  const createSampleFiles = async () => {
    try {
      // Create sample directory structure
      await containerAPI.executeCommand('mkdir -p /home/<USER>/src/components /home/<USER>/src/pages /home/<USER>/public');

      // Create sample files
      const files = [
        { name: 'package.json', content: '{\n  "name": "my-project",\n  "version": "1.0.0"\n}' },
        { name: 'README.md', content: '# My Project\n\nWelcome to my project!' },
        { name: 'src/App.js', content: 'import React from "react";\n\nfunction App() {\n  return <h1>Hello World!</h1>;\n}\n\nexport default App;' },
        { name: 'src/index.js', content: 'import React from "react";\nimport ReactDOM from "react-dom";\nimport App from "./App";\n\nReactDOM.render(<App />, document.getElementById("root"));' },
        { name: 'src/components/Header.jsx', content: 'import React from "react";\n\nconst Header = () => {\n  return <header>My Header</header>;\n};\n\nexport default Header;' },
        { name: 'public/index.html', content: '<!DOCTYPE html>\n<html>\n<head>\n  <title>My App</title>\n</head>\n<body>\n  <div id="root"></div>\n</body>\n</html>' }
      ];

      for (const file of files) {
        const encodedContent = btoa(file.content);
        await containerAPI.executeCommand(`echo '${encodedContent}' | base64 -d > /home/<USER>/${file.name}`);
      }
    } catch (error) {
      console.error('Failed to create sample files:', error);
    }
  };

  const getSampleTree = () => {
    return [
      {
        name: 'src',
        path: '/home/<USER>/src',
        type: 'folder',
        children: [
          {
            name: 'components',
            path: '/home/<USER>/src/components',
            type: 'folder',
            children: [
              { name: 'Header.jsx', path: '/home/<USER>/src/components/Header.jsx', type: 'file', children: [], level: 2 }
            ],
            level: 1
          },
          { name: 'App.js', path: '/home/<USER>/src/App.js', type: 'file', children: [], level: 1 },
          { name: 'index.js', path: '/home/<USER>/src/index.js', type: 'file', children: [], level: 1 }
        ],
        level: 0
      },
      {
        name: 'public',
        path: '/home/<USER>/public',
        type: 'folder',
        children: [
          { name: 'index.html', path: '/home/<USER>/public/index.html', type: 'file', children: [], level: 1 }
        ],
        level: 0
      },
      { name: 'package.json', path: '/home/<USER>/package.json', type: 'file', children: [], level: 0 },
      { name: 'README.md', path: '/home/<USER>/README.md', type: 'file', children: [], level: 0 }
    ];
  };



  const toggleFolder = async (path) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
      setExpandedFolders(newExpanded);
    } else {
      // Add to loading state
      setLoadingFolders(prev => new Set([...prev, path]));

      try {
        // Load folder contents when expanding
        await loadFolderContents(path);
        newExpanded.add(path);
        setExpandedFolders(newExpanded);
      } catch (error) {
        console.error('Failed to load folder contents:', error);
      } finally {
        // Remove from loading state
        setLoadingFolders(prev => {
          const newSet = new Set(prev);
          newSet.delete(path);
          return newSet;
        });
      }
    }
  };

  const loadFolderContents = async (folderPath) => {
    try {
      console.log('Loading folder contents for:', folderPath);

      // Use a more reliable command to list directory contents
      const result = await containerAPI.executeCommand(`ls -la "${folderPath}" 2>/dev/null`);

      console.log('Raw command result:', result);

      if (result.success && result.data && result.data.output) {
        const output = result.data.output;
        console.log('Folder contents output:', output);

        // Check if the output contains actual directory listing
        if (output.trim() && !output.includes('No such file or directory')) {
          const folderItems = parseDirectoryListing(output, folderPath);
          console.log('Parsed folder items:', folderItems);

          // Update the tree data to include the folder contents
          setTreeData(prevTree => {
            const updatedTree = updateTreeWithFolderContents(prevTree, folderPath, folderItems);
            console.log('Updated tree:', updatedTree);
            return updatedTree;
          });
        } else {
          console.log('Folder appears to be empty or inaccessible');
          // Still update the tree to show empty folder
          setTreeData(prevTree => updateTreeWithFolderContents(prevTree, folderPath, []));
        }
      } else {
        console.error('Failed to get folder contents:', result);
      }
    } catch (error) {
      console.error('Failed to load folder contents:', error);
    }
  };

  const updateTreeWithFolderContents = (tree, folderPath, newItems) => {
    console.log('Updating tree for path:', folderPath, 'with items:', newItems);

    return tree.map(item => {
      console.log('Checking item:', item.path, 'against target:', folderPath);

      if (item.path === folderPath) {
        console.log('Found matching path, updating children');
        return { ...item, children: newItems };
      } else if (item.children && item.children.length > 0) {
        // Recursively search in children
        const updatedChildren = updateTreeWithFolderContents(item.children, folderPath, newItems);
        return { ...item, children: updatedChildren };
      }
      return item;
    });
  };

  const handleItemClick = (item) => {
    if (item.type === 'folder') {
      toggleFolder(item.path);
    } else {
      onFileSelect(item);
    }
  };

  const handleContextMenu = (e, item) => {
    e.preventDefault();
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      item
    });
  };

  const createNewFile = async () => {
    const fileName = prompt('Enter file name (e.g., app.js, index.html, style.css):');
    if (fileName && fileName.trim()) {
      try {
        // Generate appropriate content based on file extension
        let content = '';
        const ext = fileName.split('.').pop()?.toLowerCase();

        switch (ext) {
          case 'js':
          case 'jsx':
            content = `// ${fileName}\nimport React from 'react';\n\nconst Component = () => {\n  return (\n    <div>\n      <h1>Hello from ${fileName}!</h1>\n    </div>\n  );\n};\n\nexport default Component;\n`;
            break;
          case 'html':
            content = `<!DOCTYPE html>\n<html lang="en">\n<head>\n  <meta charset="UTF-8">\n  <meta name="viewport" content="width=device-width, initial-scale=1.0">\n  <title>${fileName.replace('.html', '')}</title>\n</head>\n<body>\n  <h1>Welcome to ${fileName}</h1>\n</body>\n</html>\n`;
            break;
          case 'css':
            content = `/* ${fileName} */\nbody {\n  font-family: Arial, sans-serif;\n  margin: 0;\n  padding: 20px;\n  background-color: #f5f5f5;\n}\n\nh1 {\n  color: #333;\n  text-align: center;\n}\n`;
            break;
          case 'json':
            content = `{\n  "name": "${fileName.replace('.json', '')}",\n  "version": "1.0.0",\n  "description": "Generated by CodeForge"\n}\n`;
            break;
          case 'md':
            content = `# ${fileName.replace('.md', '')}\n\nWelcome to your new markdown file!\n\n## Features\n\n- Easy to edit\n- Great for documentation\n- Supports formatting\n\n**Created with CodeForge** 🚀\n`;
            break;
          case 'py':
            content = `# ${fileName}\nprint("Hello from ${fileName}!")\n\ndef main():\n    """Main function"""\n    print("Welcome to Python!")\n\nif __name__ == "__main__":\n    main()\n`;
            break;
          default:
            content = `// ${fileName}\nconsole.log("Hello from ${fileName}!");\n`;
        }

        const encodedContent = btoa(content);
        const result = await containerAPI.executeCommand(`mkdir -p /home/<USER>'${encodedContent}' | base64 -d > /home/<USER>/${fileName.trim()}`);
        if (result.success) {
          loadFileTree();
          setTimeout(() => {
            const newFile = { name: fileName.trim(), path: `/home/<USER>/${fileName.trim()}`, type: 'file' };
            onFileSelect(newFile);
          }, 500);
        } else {
          alert('Failed to create file. Please try again.');
        }
      } catch (error) {
        console.error('Error creating file:', error);
        alert('Failed to create file: ' + error.message);
      }
    }
    setContextMenu(null);
  };

  const createNewFolder = async () => {
    const folderName = prompt('Enter folder name (e.g., components, utils, assets):');
    if (folderName && folderName.trim()) {
      try {
        const result = await containerAPI.executeCommand(`mkdir -p /home/<USER>/${folderName.trim()}`);
        if (result.success) {
          loadFileTree();
        } else {
          alert('Failed to create folder. Please try again.');
        }
      } catch (error) {
        console.error('Error creating folder:', error);
        alert('Failed to create folder: ' + error.message);
      }
    }
    setContextMenu(null);
  };

  const handleCreateFile = async (parentPath) => {
    const fileName = prompt('Enter file name (e.g., component.jsx, utils.js, style.css):');
    if (fileName && fileName.trim()) {
      try {
        const fullPath = `${parentPath}/${fileName.trim()}`;
        const result = await containerAPI.executeCommand(`touch "${fullPath}"`);
        if (result.success) {
          await loadFolderContents(parentPath);
          // Auto-select the new file
          setTimeout(() => {
            const newFile = { name: fileName.trim(), path: fullPath, type: 'file' };
            onFileSelect(newFile);
          }, 500);
        } else {
          alert('Failed to create file. Please try again.');
        }
      } catch (error) {
        console.error('Error creating file:', error);
        alert('Failed to create file: ' + error.message);
      }
    }
  };

  const handleCreateFolder = async (parentPath) => {
    const folderName = prompt('Enter folder name:');
    if (folderName && folderName.trim()) {
      try {
        const fullPath = `${parentPath}/${folderName.trim()}`;
        const result = await containerAPI.executeCommand(`mkdir -p "${fullPath}"`);
        if (result.success) {
          await loadFolderContents(parentPath);
        } else {
          alert('Failed to create folder. Please try again.');
        }
      } catch (error) {
        console.error('Error creating folder:', error);
        alert('Failed to create folder: ' + error.message);
      }
    }
  };

  const renderTreeItem = (item, depth = 0) => {
    const isExpanded = expandedFolders.has(item.path);
    const isSelected = currentFile?.path === item.path;
    const fileIcon = getFileIcon(item.name, item.type === 'folder');
    
    return (
      <div key={item.path}>
        <div
          className={`flex items-center h-7 cursor-pointer text-sm group relative ${
            isSelected
              ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-l-2 border-blue-500'
              : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
          }`}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
          onClick={() => handleItemClick(item)}
          onContextMenu={(e) => handleContextMenu(e, item)}
          onDoubleClick={() => item.type === 'folder' && toggleFolder(item.path)}
        >
          {/* Expand/Collapse Icon */}
          {item.type === 'folder' && (
            <div
              className="w-4 h-4 flex items-center justify-center mr-1 flex-shrink-0 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
              onClick={(e) => {
                e.stopPropagation();
                toggleFolder(item.path);
              }}
            >
              {loadingFolders.has(item.path) ? (
                <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin" />
              ) : isExpanded ? (
                <FiChevronDown size={10} className="text-gray-500" />
              ) : (
                <FiChevronRight size={10} className="text-gray-500" />
              )}
            </div>
          )}
          {item.type === 'file' && <div className="w-5 mr-1 flex-shrink-0"></div>}

          {/* Folder/File Icon */}
          <div className="w-4 h-4 flex items-center justify-center mr-2 flex-shrink-0">
            {item.type === 'folder' ? (
              fileIcon ? (
                <span className="text-xs leading-none">{fileIcon}</span>
              ) : isExpanded ? (
                <FiFolder size={14} className="text-blue-600 dark:text-blue-400 opacity-80" />
              ) : (
                <FiFolder size={14} className="text-blue-600 dark:text-blue-400" />
              )
            ) : (
              <span className="text-xs leading-none">{fileIcon.icon}</span>
            )}
          </div>
          
          {/* File/Folder Name */}
          <span className="flex-1 truncate">{item.name}</span>
          
          {/* Hover Actions */}
          <div className="opacity-0 group-hover:opacity-100 flex items-center ml-1">
            {item.type === 'folder' && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCreateFile(item.path);
                  }}
                  className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
                  title="New File"
                >
                  <FiFile size={12} />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCreateFolder(item.path);
                  }}
                  className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
                  title="New Folder"
                >
                  <FiFolder size={12} />
                </button>
              </>
            )}
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleContextMenu(e, item);
              }}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
            >
              <FiMoreHorizontal size={12} />
            </button>
          </div>
        </div>
        
        {/* Render Children */}
        {item.type === 'folder' && isExpanded && (
          <div>
            {item.children && item.children.length > 0 ? (
              item.children.map(child => renderTreeItem(child, depth + 1))
            ) : (
              !loadingFolders.has(item.path) && (
                <div
                  className="text-xs text-gray-500 dark:text-gray-400 italic py-1"
                  style={{ paddingLeft: `${(depth + 1) * 16 + 24}px` }}
                >
                  Empty folder
                </div>
              )
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-900 text-sm">
      {/* Header */}
      <div className="flex items-center justify-between px-3 py-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center space-x-2">
          <FiFolder size={14} className="text-blue-500" />
          <span className="text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wide">
            Explorer
          </span>
        </div>
        <div className="flex items-center space-x-1">
          <button
            onClick={createNewFile}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded opacity-70 hover:opacity-100"
            title="New File (Ctrl+N)"
          >
            <FiPlus size={12} />
          </button>
          <button
            onClick={createNewFolder}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded opacity-70 hover:opacity-100"
            title="New Folder (Ctrl+Shift+N)"
          >
            <FiFolder size={12} />
          </button>
          <button
            onClick={loadFileTree}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded opacity-70 hover:opacity-100"
            title="Refresh Explorer"
          >
            <FiRefreshCw size={12} className={loading ? 'animate-spin' : ''} />
          </button>
        </div>
      </div>

      {/* Workspace Section */}
      <div className="px-3 py-1 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
          <FiChevronDown size={12} className="mr-1" />
          <span className="font-medium">CODEFORGE</span>
        </div>
      </div>
      
      {/* File Tree */}
      <div className="flex-1 overflow-auto">
        {loading ? (
          <div className="p-4 text-center text-gray-500">
            <FiRefreshCw className="animate-spin mx-auto mb-2" size={16} />
            <p className="text-xs">Loading...</p>
          </div>
        ) : treeData.length > 0 ? (
          <div className="py-1">
            {treeData.map(item => renderTreeItem(item, 0))}
          </div>
        ) : (
          <div className="p-4 text-center text-gray-500">
            <p className="text-xs mb-2">No files found</p>
            <button
              onClick={createNewFile}
              className="text-xs text-blue-500 hover:text-blue-600"
            >
              Create your first file
            </button>
          </div>
        )}
      </div>
      
      {/* Context Menu */}
      {contextMenu && (
        <>
          <div
            className="fixed inset-0 z-40"
            onClick={() => setContextMenu(null)}
          />
          <div
            className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg py-1 min-w-32"
            style={{
              left: contextMenu.x,
              top: contextMenu.y,
            }}
          >
            <button
              onClick={createNewFile}
              className="block w-full text-left px-3 py-1.5 text-xs hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              New File
            </button>
            <button
              onClick={createNewFolder}
              className="block w-full text-left px-3 py-1.5 text-xs hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              New Folder
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default VSCodeFileTree;
