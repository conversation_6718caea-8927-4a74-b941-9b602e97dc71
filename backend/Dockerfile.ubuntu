FROM ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV HOME=/home/<USER>
ENV PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/.local/bin

# Install essential development tools
RUN apt-get update && apt-get install -y \
    # Basic tools
    bash \
    curl \
    wget \
    git \
    vim \
    nano \
    tree \
    htop \
    unzip \
    zip \
    build-essential \
    sudo \
    # Programming languages
    python3 \
    python3-pip \
    python3-venv \
    nodejs \
    npm \
    # Additional tools
    sqlite3 \
    # Network tools
    net-tools \
    iputils-ping \
    # File tools
    file \
    less \
    grep \
    sed \
    gawk \
    && rm -rf /var/lib/apt/lists/*

# Install global npm packages
RUN npm install -g \
    yarn \
    typescript \
    ts-node \
    nodemon \
    create-react-app

# Install Python packages
RUN pip3 install \
    flask \
    django \
    fastapi \
    requests \
    black \
    flake8

# Create user with proper shell and sudo access
RUN useradd -m -s /bin/bash user && \
    usermod -aG sudo user && \
    echo "user ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Create welcome files as root, then change ownership
RUN echo "Welcome to CodeForge Development Environment!" > /home/<USER>/welcome.txt && \
    echo "# CodeForge Projects" > /home/<USER>/README.md && \
    echo "This is your personal development container." >> /home/<USER>/README.md && \
    echo "" >> /home/<USER>/README.md && \
    echo "Available tools:" >> /home/<USER>/README.md && \
    echo "- Python 3 with pip" >> /home/<USER>/README.md && \
    echo "- Node.js with npm and yarn" >> /home/<USER>/README.md && \
    echo "- Git for version control" >> /home/<USER>/README.md && \
    echo "- Vim and Nano editors" >> /home/<USER>/README.md && \
    echo "- Build tools (gcc, make)" >> /home/<USER>/README.md && \
    echo "" >> /home/<USER>/README.md && \
    echo "Try: npm init, python3 -m venv myenv, git init" >> /home/<USER>/README.md && \
    mkdir -p /home/<USER>/projects && \
    chown -R user:user /home/<USER>

WORKDIR /home/<USER>
USER user

# Keep container running
CMD ["tail", "-f", "/dev/null"]