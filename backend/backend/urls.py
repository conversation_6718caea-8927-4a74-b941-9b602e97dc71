from django.contrib import admin
from django.urls import path, include
from rest_framework_simplejwt.views import TokenRefreshView
from containers.views import CustomTokenObtainPairView, SignupView, ContainerStatusView, ExecuteCommandView

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/signup/', SignupView.as_view(), name='signup'),
    path('api/token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('api/container/status/', ContainerStatusView.as_view(), name='container_status'),
    path('api/container/execute/', ExecuteCommandView.as_view(), name='execute_command'),
]