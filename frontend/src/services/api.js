import axios from 'axios';

// API Configuration
const API_BASE_URL = 'http://localhost:8000/api';
const WS_BASE_URL = 'ws://localhost:8000/ws';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management
const getToken = () => localStorage.getItem('access_token');
const getRefreshToken = () => localStorage.getItem('refresh_token');
const setTokens = (accessToken, refreshToken) => {
  localStorage.setItem('access_token', accessToken);
  localStorage.setItem('refresh_token', refreshToken);
};
const clearTokens = () => {
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('user');
};

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = getRefreshToken();
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/token/refresh/`, {
            refresh: refreshToken,
          });
          
          const { access } = response.data;
          localStorage.setItem('access_token', access);
          
          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        clearTokens();
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  signup: async (username, email, password) => {
    try {
      const response = await api.post('/signup/', { username, email, password });
      const { tokens, user } = response.data;
      setTokens(tokens.access, tokens.refresh);

      // Store user info
      localStorage.setItem('user', JSON.stringify(user));

      return { success: true, data: response.data };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.error || 'Signup failed'
      };
    }
  },

  login: async (username, password) => {
    try {
      const response = await api.post('/token/', { username, password });
      const { access, refresh } = response.data;
      setTokens(access, refresh);

      // Store user info (you might want to decode JWT or fetch user profile)
      localStorage.setItem('user', JSON.stringify({ username }));

      return { success: true, data: response.data };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || 'Login failed'
      };
    }
  },

  logout: () => {
    clearTokens();
    window.location.href = '/';
  },

  isAuthenticated: () => {
    const token = getToken();
    if (!token) return false;
    
    try {
      // Basic JWT expiry check (you might want to use a JWT library)
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 > Date.now();
    } catch {
      return false;
    }
  },

  getUser: () => {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  }
};

// Container API
export const containerAPI = {
  getStatus: async () => {
    try {
      const response = await api.get('/container/status/');
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Failed to get container status' 
      };
    }
  },

  executeCommand: async (command) => {
    try {
      const response = await api.post('/container/execute/', { command });
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Failed to execute command' 
      };
    }
  }
};

// WebSocket Terminal Connection
export class TerminalWebSocket {
  constructor(onMessage, onError, onClose) {
    this.onMessage = onMessage;
    this.onError = onError;
    this.onClose = onClose;
    this.ws = null;
  }

  connect() {
    const token = getToken();
    if (!token) {
      this.onError('No authentication token');
      return;
    }

    try {
      // For now, we'll use a simple WebSocket connection
      // In production, you'd want to implement proper WebSocket authentication
      this.ws = new WebSocket(`${WS_BASE_URL}/terminal/`);

      this.ws.onopen = () => {
        console.log('Terminal WebSocket connected');
        // Send authentication token after connection
        this.ws.send(JSON.stringify({ type: 'auth', token: token }));
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          if (data.output !== undefined) {
            this.onMessage(data.output);
          } else if (data.message !== undefined) {
            this.onMessage(data.message);
          } else if (data.error !== undefined) {
            this.onMessage(`Error: ${data.error}\r\n`);
          } else {
            this.onMessage(event.data);
          }
        } catch (error) {
          // Handle raw text messages
          this.onMessage(event.data);
        }
      };

      this.ws.onerror = (error) => {
        console.error('Terminal WebSocket error:', error);
        this.onError('WebSocket connection error');
      };

      this.ws.onclose = (event) => {
        console.log('Terminal WebSocket closed:', event.code, event.reason);
        this.onClose();
      };
    } catch (error) {
      this.onError('Failed to create WebSocket connection');
    }
  }

  sendCommand(command) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      // Send raw input for terminal interaction
      this.ws.send(command);
    } else {
      this.onError('WebSocket not connected');
    }
  }

  sendRawInput(input) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(input);
    } else {
      this.onError('WebSocket not connected');
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

export default api;
