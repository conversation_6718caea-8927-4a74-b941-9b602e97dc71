"""
Project template system for CodeForge
Provides easy creation of various project types
"""

import asyncio
import json
from .api import containerAPI

class ProjectTemplates:
    """Handles creation of different project templates"""
    
    @staticmethod
    async def create_react_vite_project(project_name="my-react-app"):
        """Create a React project using Vite"""
        commands = [
            f"npm create vite@latest {project_name} -- --template react",
            f"cd {project_name} && npm install"
        ]
        return await ProjectTemplates._execute_commands(commands, project_name)
    
    @staticmethod
    async def create_vue_project(project_name="my-vue-app"):
        """Create a Vue.js project"""
        commands = [
            f"npm create vue@latest {project_name}",
            f"cd {project_name} && npm install"
        ]
        return await ProjectTemplates._execute_commands(commands, project_name)
    
    @staticmethod
    async def create_next_project(project_name="my-next-app"):
        """Create a Next.js project"""
        commands = [
            f"npx create-next-app@latest {project_name} --typescript --tailwind --eslint --app --src-dir --import-alias '@/*'",
            f"cd {project_name} && npm install"
        ]
        return await ProjectTemplates._execute_commands(commands, project_name)
    
    @staticmethod
    async def create_django_project(project_name="my-django-app"):
        """Create a Django project"""
        commands = [
            f"mkdir {project_name}",
            f"cd {project_name} && python3 -m venv venv",
            f"cd {project_name} && source venv/bin/activate && pip install django djangorestframework",
            f"cd {project_name} && source venv/bin/activate && django-admin startproject {project_name} .",
            f"cd {project_name} && echo 'Django project created! Run: source venv/bin/activate && python manage.py runserver' > README.md"
        ]
        return await ProjectTemplates._execute_commands(commands, project_name)
    
    @staticmethod
    async def create_flask_project(project_name="my-flask-app"):
        """Create a Flask project"""
        commands = [
            f"mkdir {project_name}",
            f"cd {project_name} && python3 -m venv venv",
            f"cd {project_name} && source venv/bin/activate && pip install flask flask-cors",
        ]
        
        # Create Flask app structure
        flask_app_content = '''from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

@app.route('/')
def hello():
    return jsonify({"message": "Hello from Flask!"})

@app.route('/api/health')
def health():
    return jsonify({"status": "healthy"})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
'''
        
        requirements_content = '''flask==2.3.3
flask-cors==4.0.0
'''
        
        readme_content = f'''# {project_name}

A Flask web application.

## Setup

1. Activate virtual environment:
   ```bash
   source venv/bin/activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Run the application:
   ```bash
   python app.py
   ```

The app will be available at http://localhost:5000
'''
        
        # Add file creation commands
        commands.extend([
            f"cd {project_name} && echo '{flask_app_content}' > app.py",
            f"cd {project_name} && echo '{requirements_content}' > requirements.txt",
            f"cd {project_name} && echo '{readme_content}' > README.md"
        ])
        
        return await ProjectTemplates._execute_commands(commands, project_name)
    
    @staticmethod
    async def create_java_maven_project(project_name="my-java-app"):
        """Create a Java Maven project"""
        commands = [
            f"mvn archetype:generate -DgroupId=com.example.{project_name} -DartifactId={project_name} -DarchetypeArtifactId=maven-archetype-quickstart -DinteractiveMode=false",
            f"cd {project_name} && echo 'Java Maven project created! Run: mvn compile exec:java' >> README.md"
        ]
        return await ProjectTemplates._execute_commands(commands, project_name)
    
    @staticmethod
    async def create_express_project(project_name="my-express-app"):
        """Create an Express.js project"""
        commands = [
            f"mkdir {project_name}",
            f"cd {project_name} && npm init -y",
            f"cd {project_name} && npm install express cors helmet morgan",
            f"cd {project_name} && npm install --save-dev nodemon"
        ]
        
        # Create Express app structure
        express_app_content = '''const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Hello from Express!' });
});

app.get('/api/health', (req, res) => {
  res.json({ status: 'healthy' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
'''
        
        package_json_scripts = '''{
  "scripts": {
    "start": "node app.js",
    "dev": "nodemon app.js",
    "test": "echo \\"Error: no test specified\\" && exit 1"
  }
}'''
        
        readme_content = f'''# {project_name}

An Express.js web application.

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Run in development mode:
   ```bash
   npm run dev
   ```

3. Run in production mode:
   ```bash
   npm start
   ```

The app will be available at http://localhost:3000
'''
        
        # Add file creation commands
        commands.extend([
            f"cd {project_name} && echo '{express_app_content}' > app.js",
            f"cd {project_name} && echo '{readme_content}' > README.md"
        ])
        
        return await ProjectTemplates._execute_commands(commands, project_name)
    
    @staticmethod
    async def create_html_project(project_name="my-html-app"):
        """Create a simple HTML/CSS/JS project"""
        commands = [f"mkdir {project_name}"]
        
        html_content = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{project_name}</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Welcome to {project_name}!</h1>
        <p>This is a simple HTML/CSS/JavaScript project.</p>
        <button id="clickBtn">Click me!</button>
        <p id="message"></p>
    </div>
    <script src="script.js"></script>
</body>
</html>'''
        
        css_content = '''/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    text-align: center;
    max-width: 500px;
}

h1 {
    color: #333;
    margin-bottom: 1rem;
}

p {
    color: #666;
    margin-bottom: 1rem;
}

button {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s;
}

button:hover {
    background: #5a6fd8;
}

#message {
    margin-top: 1rem;
    font-weight: bold;
    color: #667eea;
}'''
        
        js_content = '''// JavaScript for the project
document.addEventListener('DOMContentLoaded', function() {
    const button = document.getElementById('clickBtn');
    const message = document.getElementById('message');
    let clickCount = 0;
    
    button.addEventListener('click', function() {
        clickCount++;
        message.textContent = `Button clicked ${clickCount} time${clickCount !== 1 ? 's' : ''}!`;
        
        // Add some fun effects
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 100);
    });
    
    console.log('Project loaded successfully!');
});'''
        
        readme_content = f'''# {project_name}

A simple HTML/CSS/JavaScript project.

## Files

- `index.html` - Main HTML file
- `style.css` - Stylesheet
- `script.js` - JavaScript functionality

## Usage

Open `index.html` in your browser to view the project.

## Features

- Responsive design
- Modern CSS with gradients and shadows
- Interactive JavaScript
- Clean, professional layout
'''
        
        # Add file creation commands
        commands.extend([
            f"cd {project_name} && echo '{html_content}' > index.html",
            f"cd {project_name} && echo '{css_content}' > style.css",
            f"cd {project_name} && echo '{js_content}' > script.js",
            f"cd {project_name} && echo '{readme_content}' > README.md"
        ])
        
        return await ProjectTemplates._execute_commands(commands, project_name)
    
    @staticmethod
    async def _execute_commands(commands, project_name):
        """Execute a list of commands and return results"""
        results = []
        for command in commands:
            try:
                # Here you would use your container API to execute the command
                # For now, we'll return a success message
                results.append(f"✅ Executed: {command}")
            except Exception as e:
                results.append(f"❌ Failed: {command} - {str(e)}")
        
        return {
            "success": True,
            "project_name": project_name,
            "commands_executed": len(commands),
            "results": results
        }
    
    @staticmethod
    def get_available_templates():
        """Get list of available project templates"""
        return {
            "react-vite": "React app with Vite (modern, fast)",
            "vue": "Vue.js application",
            "next": "Next.js React framework",
            "django": "Django Python web framework",
            "flask": "Flask Python micro-framework",
            "express": "Express.js Node.js framework",
            "java-maven": "Java project with Maven",
            "html": "Simple HTML/CSS/JavaScript project"
        }
