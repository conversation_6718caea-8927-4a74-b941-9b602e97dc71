import { useState, useEffect } from 'react';
import { 
  FiFolder, 
  FiFile, 
  FiChevronRight, 
  FiChevronDown, 
  FiPlus,
  FiRefreshCw,
  FiEdit3,
  FiTrash2
} from 'react-icons/fi';
import { containerAPI } from '../services/api';

const FileExplorer = ({ onFileSelect, currentFile }) => {
  const [files, setFiles] = useState([]);
  const [expandedFolders, setExpandedFolders] = useState(new Set(['']));
  const [loading, setLoading] = useState(false);
  const [contextMenu, setContextMenu] = useState(null);

  useEffect(() => {
    loadFiles();
  }, []);

  const loadFiles = async () => {
    setLoading(true);
    try {
      // Use ls command to list files and directories
      const result = await containerAPI.executeCommand('ls -la /home/<USER>');
      if (result.success) {
        const output = result.data.output;
        const lines = output.split('\n').filter(line =>
          line.trim() &&
          !line.includes('$') &&
          !line.startsWith('total') &&
          !line.includes('drwx') // Skip current and parent directory entries
        );

        const fileList = [];
        lines.forEach(line => {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 9) {
            const permissions = parts[0];
            const name = parts.slice(8).join(' ');

            if (name !== '.' && name !== '..') {
              const isDirectory = permissions.startsWith('d');
              fileList.push({
                name: name,
                path: name,
                type: isDirectory ? 'folder' : 'file',
                children: {}
              });
            }
          }
        });

        setFiles(fileList);
      }
    } catch (error) {
      console.error('Failed to load files:', error);
      // Create some default files if none exist
      setFiles([
        { name: 'welcome.txt', path: 'welcome.txt', type: 'file', children: {} },
        { name: 'projects', path: 'projects', type: 'folder', children: {} }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const buildFileTree = (filePaths) => {
    const tree = {};
    
    filePaths.forEach(path => {
      const parts = path.split('/');
      let current = tree;
      
      parts.forEach((part, index) => {
        if (!current[part]) {
          current[part] = {
            name: part,
            path: parts.slice(0, index + 1).join('/'),
            type: index === parts.length - 1 ? 'file' : 'folder',
            children: {}
          };
        }
        current = current[part].children;
      });
    });

    return Object.values(tree);
  };

  const toggleFolder = (path) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedFolders(newExpanded);
  };

  const handleFileClick = (file) => {
    if (file.type === 'file') {
      onFileSelect(file);
    } else {
      toggleFolder(file.path);
    }
  };

  const handleContextMenu = (e, file) => {
    e.preventDefault();
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      file
    });
  };

  const createNewFile = async () => {
    const fileName = prompt('Enter file name (e.g., app.js, index.html, main.py):');
    if (fileName && fileName.trim()) {
      try {
        // Create file with appropriate template content
        let content = '';
        const ext = fileName.split('.').pop().toLowerCase();

        switch (ext) {
          case 'js':
            content = `// ${fileName}\nconsole.log('Hello from ${fileName}!');\n`;
            break;
          case 'py':
            content = `# ${fileName}\nprint("Hello from ${fileName}!")\n`;
            break;
          case 'html':
            content = `<!DOCTYPE html>\n<html lang="en">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>${fileName.replace('.html', '')}</title>\n</head>\n<body>\n    <h1>Welcome to ${fileName.replace('.html', '')}</h1>\n</body>\n</html>\n`;
            break;
          case 'css':
            content = `/* ${fileName} */\nbody {\n    font-family: Arial, sans-serif;\n    margin: 0;\n    padding: 20px;\n}\n`;
            break;
          case 'json':
            content = `{\n  "name": "${fileName.replace('.json', '')}",\n  "version": "1.0.0"\n}\n`;
            break;
          case 'md':
            content = `# ${fileName.replace('.md', '')}\n\nWelcome to your new markdown file!\n`;
            break;
          default:
            content = `// ${fileName}\n`;
        }

        const escapedContent = content.replace(/'/g, "'\"'\"'");
        const result = await containerAPI.executeCommand(`echo '${escapedContent}' > /home/<USER>/${fileName.trim()}`);

        if (result.success) {
          loadFiles();
          // Auto-select the new file
          setTimeout(() => {
            const newFile = { name: fileName.trim(), path: fileName.trim(), type: 'file' };
            onFileSelect(newFile);
          }, 500);
        } else {
          alert('Failed to create file: ' + result.error);
        }
      } catch (error) {
        alert('Failed to create file: ' + error.message);
      }
    }
    setContextMenu(null);
  };

  const createNewFolder = async () => {
    const folderName = prompt('Enter folder name (e.g., src, components, utils):');
    if (folderName && folderName.trim()) {
      try {
        const result = await containerAPI.executeCommand(`mkdir -p /home/<USER>/${folderName.trim()}`);
        if (result.success) {
          loadFiles();
        } else {
          alert('Failed to create folder: ' + result.error);
        }
      } catch (error) {
        alert('Failed to create folder: ' + error.message);
      }
    }
    setContextMenu(null);
  };

  const renameFile = async (file) => {
    const newName = prompt(`Rename "${file.name}" to:`, file.name);
    if (newName && newName.trim() && newName !== file.name) {
      try {
        const result = await containerAPI.executeCommand(`mv /home/<USER>/${file.path} /home/<USER>/${newName.trim()}`);
        if (result.success) {
          loadFiles();
        } else {
          alert('Failed to rename: ' + result.error);
        }
      } catch (error) {
        alert('Failed to rename: ' + error.message);
      }
    }
    setContextMenu(null);
  };

  const createProject = async () => {
    const projectType = prompt('Create project:\n1. React App (react)\n2. Node.js App (node)\n3. Python Project (python)\n4. HTML Project (html)\n\nEnter type:');
    const projectName = prompt('Enter project name:');

    if (projectType && projectName && projectName.trim()) {
      try {
        let command = '';
        switch (projectType.toLowerCase()) {
          case 'react':
            command = `cd /home/<USER>
            break;
          case 'node':
            command = `cd /home/<USER>
            break;
          case 'python':
            command = `cd /home/<USER>"# ${projectName}" > README.md && echo "print('Hello from ${projectName}!')" > main.py`;
            break;
          case 'html':
            command = `cd /home/<USER>"<!DOCTYPE html><html><head><title>${projectName}</title></head><body><h1>Welcome to ${projectName}</h1></body></html>" > index.html && echo "body { font-family: Arial, sans-serif; }" > style.css`;
            break;
          default:
            alert('Invalid project type. Use: react, node, python, or html');
            return;
        }

        const result = await containerAPI.executeCommand(command);
        if (result.success) {
          alert(`${projectType} project "${projectName}" created successfully!`);
          loadFiles();
        } else {
          alert('Failed to create project: ' + result.error);
        }
      } catch (error) {
        alert('Failed to create project: ' + error.message);
      }
    }
    setContextMenu(null);
  };

  const deleteFile = async (file) => {
    if (confirm(`Are you sure you want to delete ${file.name}?`)) {
      try {
        const command = file.type === 'folder' ? `rm -rf` : `rm`;
        await containerAPI.executeCommand(`${command} /home/<USER>/${file.path}`);
        loadFiles();
      } catch (error) {
        alert('Failed to delete file');
      }
    }
    setContextMenu(null);
  };

  const renderFileTree = (items, depth = 0) => {
    return items.map((item) => (
      <div key={item.path} style={{ paddingLeft: `${depth * 16}px` }}>
        <div
          className={`flex items-center py-1 px-2 hover:bg-gray-200 dark:hover:bg-gray-700 cursor-pointer text-sm group ${
            currentFile?.path === item.path ? 'bg-blue-100 dark:bg-blue-900' : ''
          }`}
          onClick={() => handleFileClick(item)}
          onContextMenu={(e) => handleContextMenu(e, item)}
        >
          {item.type === 'folder' ? (
            <>
              {expandedFolders.has(item.path) ? (
                <FiChevronDown className="mr-1 text-gray-500" />
              ) : (
                <FiChevronRight className="mr-1 text-gray-500" />
              )}
              <FiFolder className="mr-2 text-blue-500" />
            </>
          ) : (
            <>
              <span className="w-4 mr-1"></span>
              <FiFile className="mr-2 text-gray-500" />
            </>
          )}
          <span className="flex-1 truncate">{item.name}</span>
          
          {/* Action buttons on hover */}
          <div className="opacity-0 group-hover:opacity-100 flex space-x-1">
            {item.type === 'file' && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onFileSelect(item);
                }}
                className="p-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded"
              >
                <FiEdit3 className="text-xs" />
              </button>
            )}
            <button
              onClick={(e) => {
                e.stopPropagation();
                deleteFile(item);
              }}
              className="p-1 hover:bg-red-500 hover:text-white rounded"
            >
              <FiTrash2 className="text-xs" />
            </button>
          </div>
        </div>
        
        {item.type === 'folder' && 
         expandedFolders.has(item.path) && 
         Object.keys(item.children).length > 0 && (
          <div>
            {renderFileTree(Object.values(item.children), depth + 1)}
          </div>
        )}
      </div>
    ));
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-2 border-b border-gray-300 dark:border-gray-600">
        <span className="font-semibold text-sm">Files</span>
        <div className="flex space-x-1">
          <button
            onClick={createNewFile}
            className="p-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded"
            title="New File"
          >
            <FiPlus className="text-sm" />
          </button>
          <button
            onClick={createProject}
            className="p-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded text-green-600"
            title="Create Project"
          >
            🚀
          </button>
          <button
            onClick={loadFiles}
            className="p-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded"
            title="Refresh"
          >
            <FiRefreshCw className={`text-sm ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-auto">
        {loading ? (
          <div className="p-4 text-center text-gray-500">
            <FiRefreshCw className="animate-spin mx-auto mb-2" />
            Loading files...
          </div>
        ) : files.length > 0 ? (
          renderFileTree(files)
        ) : (
          <div className="p-4 text-center text-gray-500">
            <p>No files found</p>
            <button
              onClick={createNewFile}
              className="mt-2 text-blue-500 hover:text-blue-600"
            >
              Create your first file
            </button>
          </div>
        )}
      </div>

      {/* Context Menu */}
      {contextMenu && (
        <>
          <div
            className="fixed inset-0 z-40"
            onClick={() => setContextMenu(null)}
          />
          <div
            className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded shadow-lg py-1"
            style={{
              left: contextMenu.x,
              top: contextMenu.y,
            }}
          >
            <button
              onClick={createNewFile}
              className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              📄 New File
            </button>
            <button
              onClick={createNewFolder}
              className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              📁 New Folder
            </button>
            <button
              onClick={createProject}
              className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              🚀 Create Project
            </button>
            <hr className="my-1 border-gray-300 dark:border-gray-600" />
            {contextMenu.file && (
              <>
                <button
                  onClick={() => renameFile(contextMenu.file)}
                  className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  ✏️ Rename
                </button>
                <button
                  onClick={() => deleteFile(contextMenu.file)}
                  className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900"
                >
                  🗑️ Delete
                </button>
              </>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default FileExplorer;
